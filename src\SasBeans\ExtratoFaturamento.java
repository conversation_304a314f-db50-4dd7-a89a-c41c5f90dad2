/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasBeans;

/**
 *
 * <AUTHOR>
 */
public class ExtratoFaturamento {

    private String NF;
    private String Praca;
    private String TipoSrv;
    private String Serie;
    private String Data;
    private String Guia;
    private String HorarioChegada;
    private String HorarioSaida;
    private String TempoEspera;
    private String TempoEsperaQtde;
    private String Montante;
    private String MontanteQtde;
    private String AdValorem;
    private String AdValoremQtde;
    private String Embarques;
    private String EmbarquesQtde;
    private String Custodia;
    private String CustodiaQtde;
    private String Milheiros;
    private String MilheirosQtde;
    private String EnvelopesMalotes;
    private String EnvelopesMalotesQtde;
    private String ISS;
    private String ICMS;
    private String Outros;
    private String Total;
    private String TotalTempoEspera;
    private String TotalTempoEsperaQtde;
    private String TotalMontante;
    private String TotalMontanteQtde;
    private String TotalAdValorem;
    private String TotalAdValoremQtde;
    private String TotalEmbarques;
    private String TotalEmbarquesQtde;
    private String TotalCustodia;
    private String TotalCustodiaQtde;
    private String TotalMilheiros;
    private String TotalMilheirosQtde;
    private String TotalEnvelopesMalotes;
    private String TotalEnvelopesMalotesQtde;
    private String TotalISS;
    private String TotalICMS;
    private String TotalOutros;
    private String TotalTotal;
    private String Nred;

    public ExtratoFaturamento() {
        this.NF = "";
        this.Praca = "";
        this.TipoSrv = "";
        this.Data = "";
        this.Guia = "";
        this.HorarioChegada = "";
        this.HorarioSaida = "";
        this.TempoEspera = "";
        this.TempoEsperaQtde = "";
        this.Montante = "";
        this.MontanteQtde = "";
        this.AdValorem = "";
        this.AdValoremQtde = "";
        this.Embarques = "";
        this.EmbarquesQtde = "";
        this.Custodia = "";
        this.CustodiaQtde = "";
        this.Milheiros = "";
        this.MilheirosQtde = "";
        this.EnvelopesMalotes = "";
        this.EnvelopesMalotesQtde = "";
        this.ISS = "";
        this.ICMS = "";
        this.Outros = "";
        this.Total = "";
        this.Nred = "";
        this.TotalTempoEspera = "";
        this.TotalTempoEsperaQtde = "";
        this.TotalMontante = "";
        this.TotalMontanteQtde = "";
        this.TotalAdValorem = "";
        this.TotalAdValoremQtde = "";
        this.TotalEmbarques = "";
        this.TotalEmbarquesQtde = "";
        this.TotalCustodia = "";
        this.TotalCustodiaQtde = "";
        this.TotalMilheiros = "";
        this.TotalMilheirosQtde = "";
        this.TotalEnvelopesMalotes = "";
        this.TotalEnvelopesMalotesQtde = "";
        this.TotalISS = "";
        this.TotalICMS = "";
        this.TotalOutros = "";
        this.TotalTotal = "";
    }

    public String getData() {
        return Data;
    }

    public void setData(String Data) {
        this.Data = Data;
    }

    public String getGuia() {
        return Guia;
    }

    public void setGuia(String Guia) {
        this.Guia = Guia;
    }

    public String getHorarioChegada() {
        return HorarioChegada;
    }

    public void setHorarioChegada(String HorarioChegada) {
        this.HorarioChegada = HorarioChegada;
    }

    public String getHorarioSaida() {
        return HorarioSaida;
    }

    public void setHorarioSaida(String HorarioSaida) {
        this.HorarioSaida = HorarioSaida;
    }

    public String getTempoEspera() {
        return TempoEspera;
    }

    public void setTempoEspera(String TempoEspera) {
        this.TempoEspera = TempoEspera;
    }

    public String getMontante() {
        return Montante;
    }

    public void setMontante(String Montante) {
        this.Montante = Montante;
    }

    public String getAdValorem() {
        return AdValorem;
    }

    public void setAdValorem(String AdValorem) {
        this.AdValorem = AdValorem;
    }

    public String getEmbarques() {
        return Embarques;
    }

    public void setEmbarques(String Embarques) {
        this.Embarques = Embarques;
    }

    public String getCustodia() {
        return Custodia;
    }

    public void setCustodia(String Custodia) {
        this.Custodia = Custodia;
    }

    public String getMilheiros() {
        return Milheiros;
    }

    public void setMilheiros(String Milheiros) {
        this.Milheiros = Milheiros;
    }

    public String getEnvelopesMalotes() {
        return EnvelopesMalotes;
    }

    public void setEnvelopesMalotes(String EnvelopesMalotes) {
        this.EnvelopesMalotes = EnvelopesMalotes;
    }

    public String getISS() {
        return ISS;
    }

    public void setISS(String ISS) {
        this.ISS = ISS;
    }

    public String getICMS() {
        return ICMS;
    }

    public void setICMS(String ICMS) {
        this.ICMS = ICMS;
    }

    public String getOutros() {
        return Outros;
    }

    public void setOutros(String Outros) {
        this.Outros = Outros;
    }

    public String getTotal() {
        return Total;
    }

    public void setTotal(String Total) {
        this.Total = Total;
    }

    public String getTempoEsperaQtde() {
        return TempoEsperaQtde;
    }

    public void setTempoEsperaQtde(String TempoEsperaQtde) {
        this.TempoEsperaQtde = TempoEsperaQtde;
    }

    public String getMontanteQtde() {
        return MontanteQtde;
    }

    public void setMontanteQtde(String MontanteQtde) {
        this.MontanteQtde = MontanteQtde;
    }

    public String getAdValoremQtde() {
        return AdValoremQtde;
    }

    public void setAdValoremQtde(String AdValoremQtde) {
        this.AdValoremQtde = AdValoremQtde;
    }

    public String getEmbarquesQtde() {
        return EmbarquesQtde;
    }

    public void setEmbarquesQtde(String EmbarquesQtde) {
        this.EmbarquesQtde = EmbarquesQtde;
    }

    public String getCustodiaQtde() {
        return CustodiaQtde;
    }

    public void setCustodiaQtde(String CustodiaQtde) {
        this.CustodiaQtde = CustodiaQtde;
    }

    public String getMilheirosQtde() {
        return MilheirosQtde;
    }

    public void setMilheirosQtde(String MilheirosQtde) {
        this.MilheirosQtde = MilheirosQtde;
    }

    public String getEnvelopesMalotesQtde() {
        return EnvelopesMalotesQtde;
    }

    public void setEnvelopesMalotesQtde(String EnvelopesMalotesQtde) {
        this.EnvelopesMalotesQtde = EnvelopesMalotesQtde;
    }

    public String getNred() {
        return Nred;
    }

    public void setNred(String Nred) {
        this.Nred = Nred;
    }

    public String getTotalTempoEspera() {
        return TotalTempoEspera;
    }

    public void setTotalTempoEspera(String TotalTempoEspera) {
        this.TotalTempoEspera = TotalTempoEspera;
    }

    public String getTotalTempoEsperaQtde() {
        return TotalTempoEsperaQtde;
    }

    public void setTotalTempoEsperaQtde(String TotalTempoEsperaQtde) {
        this.TotalTempoEsperaQtde = TotalTempoEsperaQtde;
    }

    public String getTotalMontante() {
        return TotalMontante;
    }

    public void setTotalMontante(String TotalMontante) {
        this.TotalMontante = TotalMontante;
    }

    public String getTotalMontanteQtde() {
        return TotalMontanteQtde;
    }

    public void setTotalMontanteQtde(String TotalMontanteQtde) {
        this.TotalMontanteQtde = TotalMontanteQtde;
    }

    public String getTotalAdValorem() {
        return TotalAdValorem;
    }

    public void setTotalAdValorem(String TotalAdValorem) {
        this.TotalAdValorem = TotalAdValorem;
    }

    public String getTotalAdValoremQtde() {
        return TotalAdValoremQtde;
    }

    public void setTotalAdValoremQtde(String TotalAdValoremQtde) {
        this.TotalAdValoremQtde = TotalAdValoremQtde;
    }

    public String getTotalEmbarques() {
        return TotalEmbarques;
    }

    public void setTotalEmbarques(String TotalEmbarques) {
        this.TotalEmbarques = TotalEmbarques;
    }

    public String getTotalEmbarquesQtde() {
        return TotalEmbarquesQtde;
    }

    public void setTotalEmbarquesQtde(String TotalEmbarquesQtde) {
        this.TotalEmbarquesQtde = TotalEmbarquesQtde;
    }

    public String getTotalCustodia() {
        return TotalCustodia;
    }

    public void setTotalCustodia(String TotalCustodia) {
        this.TotalCustodia = TotalCustodia;
    }

    public String getTotalCustodiaQtde() {
        return TotalCustodiaQtde;
    }

    public void setTotalCustodiaQtde(String TotalCustodiaQtde) {
        this.TotalCustodiaQtde = TotalCustodiaQtde;
    }

    public String getTotalMilheiros() {
        return TotalMilheiros;
    }

    public void setTotalMilheiros(String TotalMilheiros) {
        this.TotalMilheiros = TotalMilheiros;
    }

    public String getTotalMilheirosQtde() {
        return TotalMilheirosQtde;
    }

    public void setTotalMilheirosQtde(String TotalMilheirosQtde) {
        this.TotalMilheirosQtde = TotalMilheirosQtde;
    }

    public String getTotalEnvelopesMalotes() {
        return TotalEnvelopesMalotes;
    }

    public void setTotalEnvelopesMalotes(String TotalEnvelopesMalotes) {
        this.TotalEnvelopesMalotes = TotalEnvelopesMalotes;
    }

    public String getTotalEnvelopesMalotesQtde() {
        return TotalEnvelopesMalotesQtde;
    }

    public void setTotalEnvelopesMalotesQtde(String TotalEnvelopesMalotesQtde) {
        this.TotalEnvelopesMalotesQtde = TotalEnvelopesMalotesQtde;
    }

    public String getTotalISS() {
        return TotalISS;
    }

    public void setTotalISS(String TotalISS) {
        this.TotalISS = TotalISS;
    }

    public String getTotalICMS() {
        return TotalICMS;
    }

    public void setTotalICMS(String TotalICMS) {
        this.TotalICMS = TotalICMS;
    }

    public String getTotalOutros() {
        return TotalOutros;
    }

    public void setTotalOutros(String TotalOutros) {
        this.TotalOutros = TotalOutros;
    }

    public String getTotalTotal() {
        return TotalTotal;
    }

    public void setTotalTotal(String TotalTotal) {
        this.TotalTotal = TotalTotal;
    }

    public String getNF() {
        return NF;
    }

    public void setNF(String NF) {
        this.NF = NF;
    }

    public String getPraca() {
        return Praca;
    }

    public void setPraca(String praca) {
        this.Praca = praca;
    }

    public String getTipoSrv() {
        return TipoSrv;
    }

    public void setTipoSrv(String tipoSrv) {
        this.TipoSrv = tipoSrv;
    }

    public String getSerie() {
        return Serie;
    }

    public void setSerie(String Serie) {
        this.Serie = Serie;
    }
}
