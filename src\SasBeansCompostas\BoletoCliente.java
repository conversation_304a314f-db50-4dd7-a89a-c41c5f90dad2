package SasBeansCompostas;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * Bean para representar dados de boleto do cliente no portal
 * 
 * <AUTHOR>
 */
public class BoletoCliente {
    
    private String url;
    private String numero;
    private String praca;
    private String nossoNumero;
    private String banco;
    private BigDecimal valor;
    private LocalDate dataVencimento;
    private String situacao;
    private String sequencia;
    private String ordem;
    
    public BoletoCliente() {
        this.valor = BigDecimal.ZERO;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getPraca() {
        return praca;
    }

    public void setPraca(String praca) {
        this.praca = praca;
    }

    public String getNossoNumero() {
        return nossoNumero;
    }

    public void setNossoNumero(String nossoNumero) {
        this.nossoNumero = nossoNumero;
    }

    public String getBanco() {
        return banco;
    }

    public void setBanco(String banco) {
        this.banco = banco;
    }

    public BigDecimal getValor() {
        return valor;
    }

    public void setValor(BigDecimal valor) {
        this.valor = valor != null ? valor : BigDecimal.ZERO;
    }

    public LocalDate getDataVencimento() {
        return dataVencimento;
    }

    public void setDataVencimento(LocalDate dataVencimento) {
        this.dataVencimento = dataVencimento;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getSequencia() {
        return sequencia;
    }

    public void setSequencia(String sequencia) {
        this.sequencia = sequencia;
    }

    public String getOrdem() {
        return ordem;
    }

    public void setOrdem(String ordem) {
        this.ordem = ordem;
    }

    /**
     * Retorna uma chave única para identificar o boleto
     * @return String no formato "numero-praca-nossonumero"
     */
    public String getChave() {
        return numero + "-" + praca + "-" + (nossoNumero != null ? nossoNumero : "");
    }
}
