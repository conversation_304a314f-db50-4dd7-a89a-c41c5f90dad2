/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasbeans;

/**
 *
 * <AUTHOR>
 */
public class CtrOperEquip {

    private String IDEquip;
    private String DtUltMov;
    private String TempoDias;
    private String SeqRota;
    private String Parada;
    private String ER;
    private String CodCli1;
    private String Hora1;
    private String Hrcheg;
    private String HrSaida;
    private String Tempoespera;
    private String Guia;
    private String Serie;
    private String Operador;
    private String Dt_alter;
    private String Hr_alter;

    private String latitude;
    private String longitude;
    private String nred;

    private String Ende;
    private String Bairro;
    private String Cidade;
    private String Uf;
    private String Cep;
    private String Motorista;

    private String Limite;
    private String LimiteLocal;
    private String NredFat;

    private String DataPrevistaColeta;
    private String DataEntrega;
    private String TempoRestante;

    private String Solicitante;
    
    private String CaminhoImagem;
    private String CaminhoVideo;
    
    private String QtdeCacambas;
    
    // Guardar dados de marketing
    private String QtdeDiasFimContrato;
    private String DtInicio;
    private String DtFinal;
    private String NRedMkt;
    private String TipoEquip;
    private String imgB64;
    
    public void setTipoEquip(String TipoEquip) {
        this.TipoEquip = TipoEquip;
    }

    public String getTipoEquip() {
        return TipoEquip;
    }

    public String getIDEquip() {
        return IDEquip;
    }

    public void setIDEquip(String IDEquip) {
        this.IDEquip = IDEquip;
    }

    public String getDtUltMov() {
        return DtUltMov;
    }

    public void setDtUltMov(String DtUltMov) {
        this.DtUltMov = DtUltMov;
    }

    public String getTempoDias() {
        return TempoDias;
    }

    public void setTempoDias(String TempoDias) {
        this.TempoDias = TempoDias;
    }

    public String getSeqRota() {
        return SeqRota;
    }

    public void setSeqRota(String SeqRota) {
        this.SeqRota = SeqRota;
    }

    public String getParada() {
        return Parada;
    }

    public void setParada(String Parada) {
        this.Parada = Parada;
    }

    public String getER() {
        return ER;
    }

    public void setER(String ER) {
        this.ER = ER;
    }

    public String getCodCli1() {
        return CodCli1;
    }

    public void setCodCli1(String CodCli1) {
        this.CodCli1 = CodCli1;
    }

    public String getHora1() {
        return Hora1;
    }

    public void setHora1(String Hora1) {
        this.Hora1 = Hora1;
    }

    public String getHrcheg() {
        return Hrcheg;
    }

    public void setHrcheg(String Hrcheg) {
        this.Hrcheg = Hrcheg;
    }

    public String getHrSaida() {
        return HrSaida;
    }

    public void setHrSaida(String HrSaida) {
        this.HrSaida = HrSaida;
    }

    public String getTempoespera() {
        return Tempoespera;
    }

    public void setTempoespera(String Tempoespera) {
        this.Tempoespera = Tempoespera;
    }

    public String getGuia() {
        return Guia;
    }

    public void setGuia(String Guia) {
        this.Guia = Guia;
    }

    public String getSerie() {
        return Serie;
    }

    public void setSerie(String Serie) {
        this.Serie = Serie;
    }

    public String getOperador() {
        return Operador;
    }

    public void setOperador(String Operador) {
        this.Operador = Operador;
    }

    public String getDt_alter() {
        return Dt_alter;
    }

    public void setDt_alter(String Dt_alter) {
        this.Dt_alter = Dt_alter;
    }

    public String getHr_alter() {
        return Hr_alter;
    }

    public void setHr_alter(String Hr_alter) {
        this.Hr_alter = Hr_alter;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getNred() {
        return nred;
    }

    public void setNred(String nred) {
        this.nred = nred;
    }

    public String getEnde() {
        return Ende;
    }

    public void setEnde(String Ende) {
        this.Ende = Ende;
    }

    public String getBairro() {
        return Bairro;
    }

    public void setBairro(String Bairro) {
        this.Bairro = Bairro;
    }

    public String getCidade() {
        return Cidade;
    }

    public void setCidade(String Cidade) {
        this.Cidade = Cidade;
    }

    public String getUf() {
        return Uf;
    }

    public void setUf(String Uf) {
        this.Uf = Uf;
    }

    public String getCep() {
        return Cep;
    }

    public void setCep(String Cep) {
        this.Cep = Cep;
    }

    public String getMotorista() {
        return Motorista;
    }

    public void setMotorista(String Motorista) {
        this.Motorista = Motorista;
    }

    public String getLimite() {
        return Limite;
    }

    public void setLimite(String Limite) {
        this.Limite = Limite;
    }

    public String getLimiteLocal() {
        return LimiteLocal;
    }

    public void setLimiteLocal(String LimiteLocal) {
        this.LimiteLocal = LimiteLocal;
    }

    public String getDataPrevistaColeta() {
        return DataPrevistaColeta;
    }

    public void setDataPrevistaColeta(String DataPrevistaColeta) {
        this.DataPrevistaColeta = DataPrevistaColeta;
    }

    public String getDataEntrega() {
        return DataEntrega;
    }

    public void setDataEntrega(String DataEntrega) {
        this.DataEntrega = DataEntrega;
    }

    public String getTempoRestante() {
        return TempoRestante;
    }

    public void setTempoRestante(String TempoRestante) {
        this.TempoRestante = TempoRestante;
    }

    public String getNredFat() {
        return NredFat;
    }

    public void setNredFat(String NredFat) {
        this.NredFat = NredFat;
    }

    public String getSolicitante() {
        return Solicitante;
    }

    public void setSolicitante(String Solicitante) {
        this.Solicitante = Solicitante;
    }

    public String getCaminhoImagem() {        
        return CaminhoImagem;
    }

    public void setCaminhoImagem(String CaminhoImagem) {
        if (CaminhoImagem == null){
           this.CaminhoImagem = "SEM CAPTURA";
        }else{
           this.CaminhoImagem = CaminhoImagem.replace(";", "");
        }
    }

    public String getCaminhoVideo() {
        return CaminhoVideo;
    }

    public void setCaminhoVideo(String CaminhoVideo) {
        if (CaminhoVideo == null){
           this.CaminhoVideo= "SEM CAPTURA";
        }else {
           this.CaminhoVideo= CaminhoVideo.replace(";", "");
        }
    }    
    
    public String getQtdeCacambas() {
        return QtdeCacambas;
    }

    public void setQtdeCacambas(String QtdeCacambas) {
        this.QtdeCacambas = QtdeCacambas;
    }

    public String getQtdeDiasFimContrato() {
        return QtdeDiasFimContrato;
    }

    public void setQtdeDiasFimContrato(String QtdeDiasFimContrato) {
        this.QtdeDiasFimContrato = QtdeDiasFimContrato;
    }

    public String getDtInicio() {
        return DtInicio;
    }

    public void setDtInicio(String DtInicio) {
        this.DtInicio = DtInicio;
    }

    public String getDtFinal() {
        return DtFinal;
    }

    public void setDtFinal(String DtFinal) {
        this.DtFinal = DtFinal;
    }

    public String getNRedMkt() {
        return NRedMkt;
    }

    public void setNRedMkt(String NRedMkt) {
        this.NRedMkt = NRedMkt;
    }
    
    public String getImgB64() {
        return imgB64;
    }

    public void setImgB64(String imgB64) {
        this.imgB64 = imgB64;
    }
    
    
}
