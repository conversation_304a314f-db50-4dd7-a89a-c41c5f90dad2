package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.Escala;
import SasBeans.Paramet;
import SasBeans.Rastrear;
import SasBeans.Rt_Escala;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.Empresas;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR> servidor para usar debug mais facilmente no integrador com
 * a golsat
 */
public class RastrearDao {

    private Boolean isTranspCacamba(String empresa) throws Exception {
        SasPoolPersistencia pool = new SasPoolPersistencia();
        pool.setCaminho("/Dados/mapconect_deploy.txt");
        Persistencia inSatellite;
        inSatellite = pool.getConexao("SATELLITE", "");

        ParametDao parametDao = new ParametDao();
        Paramet parametGoogle = parametDao.getParametGoogleApi(empresa, inSatellite);

        return parametGoogle.getTranspCacamba().equals("0") ? false : true;
    }

    Empresas empresa = new Empresas();

    /**
     * Grava rastreamento
     *
     * @param Codigo - CÃ³digo sequencial da prÃ³xima posiÃ§Ã£o na tabela -
     * buscar pelo mÃ©todo getProximoRegistro
     * @param ID_Modulo - NÃºmero identificador
     * @param Latitude - NÃºmero latitude
     * @param Longitude - NÃºmero logitude
     * @param Data - Data rastreamento
     * @param Hora - Hora rastreamento
     * @param DtTrans - Data transporte
     * @param HrTrans - Hora transporte
     * @param Satelite - CÃ³digo Satelite
     * @param persistencia - ConexÃ£o ao banco
     * @throws java.lang.Exception - pode gerar exception
     */
    public void gravarRastrear(BigDecimal Codigo, String ID_Modulo, String Latitude, String Longitude, String Data,
            String Hora, String DtTrans, String HrTrans, String Satelite, Persistencia persistencia) throws Exception {
        String sql;
        try {
            sql = "insert into rastrear"
                    + " (Codigo, ID_Modulo, Latitude, Longitude, Data, Hora, DtTrans, HrTrans, Satelite)"
                    + " values (?,?,?,?,?,?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(Codigo);
            consulta.setString(ID_Modulo);
            consulta.setString(Latitude);
            consulta.setString(Longitude);
            consulta.setString(Data);
            consulta.setString(Hora);
            consulta.setString(DtTrans);
            consulta.setString(HrTrans);
            consulta.setString(Satelite);
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao gravar tabela Rastrear - " + e.getMessage());
        }
    }

    /**
     * Retorna o cÃ³digo da prÃ³xima posiÃ§Ã£o da tabela
     *
     * @param persistencia - conexÃ£o ao banco
     * @return - prÃ³ximo cÃ³digo
     * @throws Exception
     */
    public BigDecimal getProximoRegistro(String vCodPessoa, String vDataPOS, String vHoraPOS, Persistencia persistencia) throws Exception {
        BigDecimal retorno = null;
        try {
            //Consulta consult = new Consulta("select ISNULL(MAX(codigo),0)  Codigo from rastrear ", persistencia);
            Consulta consult = new Consulta("Select max(Codigo) codigoMax, isnull((Select top 1 Codigo from rastrear where Matr = "+vCodPessoa+" and Data = '"+vDataPOS+"' and hora = '"+vHoraPOS+"'),0) Codigo from Rastrear", persistencia);
            
            consult.select();
            retorno = new BigDecimal("-1.00");
            while (consult.Proximo()) {
                try {                    
                   if (consult.getInt("Codigo") == 0){
                       retorno = consult.getBigDecimal("codigoMax");
                    }
                } catch (Exception e) {
                    retorno = new BigDecimal("-1.00");
                }
            }
            if (retorno == null) {
                retorno = new BigDecimal("-1.00");
            }
            consult.Close();
            return (retorno);
        } catch (Exception e) {
            throw new Exception("Falha ao encontrar posiÃ§Ã£o tabela Rastrear - " + e.getMessage());
        }
    }

    /**
     * Inserção de posição na tabela rastrear
     *
     * @param rastrear campos importantes: Codigo, ID_Modulo, Latitude,
     * Longitude, Data, Hora, DtTrans, HrTrans, Satelite
     * @param persistencia - conexão ao banco de dados
     * @throws Exception
     */
    public void gravarRastrear(Rastrear rastrear, Persistencia persistencia) throws Exception {
        try {
                        
            String sql = "insert into Rastrear "
                    + " (Codigo, ID_Modulo, Latitude, Longitude, Data, Hora, DtTrans, HrTrans, Satelite, "
                    + "  Placa, Velocidade, Movimento)"
                    + " values (?,?,?,?,?,?,?,?,?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(rastrear.getCodigo());
            consulta.setString(rastrear.getID_MODULO());
            consulta.setString(rastrear.getLatitude());
            consulta.setString(rastrear.getLongitude());
            consulta.setString(rastrear.getData().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            consulta.setString(rastrear.getHora());
            consulta.setString(rastrear.getDtTrans().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            consulta.setString(rastrear.getHrTrans());
            consulta.setString(rastrear.getSatelite());
            consulta.setString(rastrear.getPlaca());
            consulta.setString(rastrear.getVelocidade());
            consulta.setString(rastrear.getMovimento());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao gravar tabela Rastrear - " + e.getMessage());
        }
    }

    /**
     * Excluir todos os registros anteriores a data passada
     *
     * @param Data - serão exluidos todos os registros <= a data passada @param
     * pe rsistencia - conexão ao banco de dados @throws Exception @param
     * persistencia @throws java.lang.Exception
     */
    public void ApagarRegistrosData(LocalDate Data, Persistencia persistencia) throws Exception {
        String sql;
        try {
            sql = "delete from rastrear where data <= ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setDate(DataAtual.LC2Date(Data));
            consulta.delete();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao excluir registros tabela Rastrear - \r\n" + e.getMessage());
        }
    }

    /**
     * Grava posição com a matricula do usuário
     *
     * @param Codigo
     * @param ID_Modulo
     * @param Latitude
     * @param Longitude
     * @param Data
     * @param Hora
     * @param DtTrans
     * @param HrTrans
     * @param Satelite
     * @param Matr
     * @param velocidade
     * @param precisao
     * @param persistencia
     * @throws Exception
     */
    public void gravarRastrear(BigDecimal Codigo, String ID_Modulo, String Latitude, String Longitude, String Data,
            String Hora, String DtTrans, String HrTrans, String Satelite, String Matr, String velocidade, String precisao,
            Persistencia persistencia) throws Exception {
        String sql;
        try {
            sql = "insert into rastrear"
                    + " (Codigo, ID_Modulo, Latitude, Longitude, Data, Hora, DtTrans, HrTrans, Satelite, Matr, Velocidade, Precisao)"
                    + " values (?,?,?,?,?,?,?,?,?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(Codigo);
            consulta.setString(ID_Modulo);
            consulta.setString(Latitude);
            consulta.setString(Longitude);
            consulta.setString(Data);
            consulta.setString(Hora);
            consulta.setString(DtTrans);
            consulta.setString(HrTrans);
            consulta.setString(Satelite);
            consulta.setString(Matr);
            consulta.setString(velocidade);
            consulta.setString(precisao);
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("RastrearDao.gravarRastrear - " + e.getMessage() + "\r\n"
                    + "insert into rastrear"
                    + " (Codigo, ID_Modulo, Latitude, Longitude, Data, Hora, DtTrans, HrTrans, Satelite, Matr)"
                    + " values (" +Codigo+ "," + ID_Modulo + "," + Latitude + "," + Longitude + "," + Data + "," + Hora + "," + DtTrans + "," + HrTrans + "," + Satelite + "," + Matr + ")");
        }
    }

    /**
     * Grava posição com a matricula do usuário
     *
     * @param ID_Modulo
     * @param Latitude
     * @param Longitude
     * @param Data
     * @param Hora
     * @param DtTrans
     * @param HrTrans
     * @param Satelite
     * @param Matr
     * @param persistencia
     * @throws Exception
     */
    public void gravarRastrear(String ID_Modulo, String Latitude, String Longitude, String Data,
            String Hora, String DtTrans, String HrTrans, String Satelite, String Matr, Persistencia persistencia) throws Exception {
        String sql;
        try {
            sql = "insert into rastrear"
                    + " (Codigo, ID_Modulo, Latitude, Longitude, Data, Hora, DtTrans, HrTrans, Satelite, Matr)"
                    + " values (?,?,?,?,?,?,?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(ID_Modulo);
            consulta.setString(Latitude);
            consulta.setString(Longitude);
            consulta.setString(Data);
            consulta.setString(Hora);
            consulta.setString(DtTrans);
            consulta.setString(HrTrans);
            consulta.setString(Satelite);
            consulta.setString(Matr);
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao gravar tabela Rastrear - " + e.getMessage());
        }
    }

    public void gravarRastrearA(String ID_Modulo, String Latitude, String Longitude, String Data,
            String Hora, String DtTrans, String HrTrans, String velocidade, Persistencia persistencia) throws Exception {
        String sql;
        try {
            sql = "insert into rastrear"
                    + " (Codigo, ID_Modulo, Latitude, Longitude, Data, Hora, DtTrans, HrTrans, Velocidade)"
                    + " values ((select ISNULL(max(codigo),0) + 1 FROM Rastrear),?,?,?,?,?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(ID_Modulo);
            consulta.setString(Latitude);
            consulta.setString(Longitude);
            consulta.setString(Data);
            consulta.setString(Hora);
            consulta.setString(DtTrans);
            consulta.setString(HrTrans);
            consulta.setString(velocidade);
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("RastrearDao.gravarRastrear - " + e.getMessage() + "\r\n"
                    + "insert into rastrear"
                    + " (Codigo, ID_Modulo, Latitude, Longitude, Data, Hora, DtTrans, HrTrans, Velocidade)"
                    + " values ((select ISNULL(max(codigo),0) + 1 FROM Rastrear)," + ID_Modulo + "," + Latitude + ","
                    + Longitude + "," + Data + "," + Hora + "," + DtTrans + "," + HrTrans + "," + velocidade + ")");
        }
    }

    /**
     * Retorna a última localização do veículo
     *
     * @param seqRotas
     * @param persistencia - conexÃ£o ao banco
     * @return - prÃ³ximo cÃ³digo
     * @throws Exception
     */
    public List<Rastrear> buscarLocalizacoes(List<Escala> seqRotas, Persistencia persistencia) throws Exception {
        try {
            String sql = "  select  escala.rota, funcion.nome, rastrear.latitude, rastrear.longitude, \n"
                    + " rastrear.data, rastrear.hora, a.seqrota, escala.veiculo, veiculos.placa, VeiculosMod.Descricao ModeloVeic, funcion.nome_guer, \n"
                    + " mot.nome motNome,  \n"
                    + " (Select count(*) from Rt_PercSLA where Rt_PercSLA.Sequencia = a.SeqRota and len(isnull(Rt_PercSLA.HrChegVei,'')) \n"
                    + " > 0 and len(isnull(Rt_PercSla.HrSaidaVei,'')) = 0 ) Atendimento"
                    + " from \n"
                    + " (Select Seqrota, Max(rastrear.Data) Data, rastrearstat.codfil, Max(sequencia) sequencia\n"
                    + " from rastrearstat\n"
                    + " left join rastrear on rastrear.codigo = rastrearstat.sequencia \n";
            if (seqRotas.isEmpty()) {

            } else if (seqRotas.size() == 1) {
                sql += " where rastrearstat.seqrota = ? and rastrearstat.codfil = ? \n";
            } else {
                sql += " where \n";
                for (int i = 0; i < seqRotas.size(); i++) {
                    if (i == seqRotas.size() - 1) {
                        sql += " (rastrearstat.seqrota = ? and rastrearstat.codfil = ?) \n";
                    } else {
                        sql += " (rastrearstat.seqrota = ? and rastrearstat.codfil = ?) OR \n";
                    }
                }
            }
            sql += " Group by Seqrota, rastrearstat.codfil) a\n"
                    + " left join rastrear on rastrear.codigo = a.sequencia \n"
                    + " left join escala on escala.seqrota = a.seqrota \n"
                    + "                 and escala.codfil  = a.codfil \n"
                    + " left join funcion on funcion.matr = escala.matrche \n"
                    + "                  and funcion.codfil = escala.codfil \n"
                    + " left join funcion mot on mot.matr   = escala.matrmot \n"
                    + "                      and mot.codfil = escala.codfil \n"
                    + " left join veiculos  on veiculos.numero = escala.veiculo \n"
                    + " left join VeiculosMod  on VeiculosMod.Codigo = Veiculos.Modelo \n"
                    + " order by rastrear.dttrans desc,  rastrear.hrtrans desc \n";
            List<Rastrear> retorno = new ArrayList<>();
            Consulta consulta = new Consulta(sql, persistencia);
            for (Escala seqRota : seqRotas) {
                consulta.setBigDecimal(seqRota.getSeqRota());
                consulta.setBigDecimal(seqRota.getCodFil());
            }

            consulta.select();
            Rastrear loc;
            while (consulta.Proximo()) {
                loc = new Rastrear();

                loc.setRota(consulta.getString("rota"));
                loc.setNome(consulta.getString("nome"));
                loc.setNomeMotorista(consulta.getString("motNome"));

                loc.setData(consulta.getLocalDate("data"));
                loc.setHora(consulta.getString("hora"));
                loc.setLatitude(consulta.getString("latitude"));
                loc.setLongitude(consulta.getString("longitude"));
                loc.setPlaca(consulta.getString("placa"));
                loc.setVeiculo(consulta.getString("veiculo").replace(".0", ""));
                loc.setModeloVeiculo(consulta.getString("ModeloVeic"));

                loc.setSeqRota(consulta.getString("seqrota"));
                loc.setAtendimento(consulta.getString("Atendimento"));
                retorno.add(loc);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar localização - " + e.getMessage());
        }
    }

    /**
     * Retorna a última localização do veículo
     *
     * @param seqRota
     * @param codFil
     * @param persistencia - conexÃ£o ao banco
     * @return - prÃ³ximo cÃ³digo
     * @throws Exception
     */
    public Rastrear buscarLocalizacao(String seqRota, String codFil, Persistencia persistencia) throws Exception {
        try {
            String sql = "Declare @inSequencia FLOAT = ? ;\n"
                    + "Declare @qtdRastrer Int;\n"
                    + "Declare @cxforte Varchar(07);\n"
                    + "Declare @codpessoa int;\n"
                    + "Declare @latitude Varchar(20);\n"
                    + "Declare @longitude Varchar(20);\n"
                    + "Declare @seqRastrear int;\n"
                    + "Declare @codfil float;\n"
                    + "Declare @data Varchar(10);\n"
                    + "Declare @hora Varchar(05); \n";

            if (!isTranspCacamba(persistencia.getEmpresa())) {
                sql += "Select @qtdRastrer = Isnull(Count(*),0)\n"
                        + " from rastrear (nolock) \n"
                        + " left join rastrearstat (nolock) \n"
                        + "   on rastrear.codigo = rastrearstat.sequencia  \n"
                        + " left join Pessoa (nolock)\n"
                        + "   on rastrearstat.CodPessoa = Pessoa.Codigo\n"
                        + " left join escala (nolock)\n"
                        + "   on escala.Data = RastrearStat.Data\n"
                        + "  and escala.MatrChe = Pessoa.Matr \n"
                        + "Where Escala.SeqRota = @inSequencia;\n"
                        + "\n"
                        + "if(@qtdRastrer = 0) begin\n"
                        + "\n"
                        + "	Select top 01 @cxforte = CodCli from Cxforte (Nolock) where codfil = (Select codfil from Rotas (Nolock) where sequencia = @inSequencia) Order by DtFecha Desc;\n"
                        + "	\n"
                        + "	Select \n"
                        + "	@codpessoa = Pessoa.Codigo, \n"
                        + "	@latitude = Clientes.Latitude,\n"
                        + "	@longitude = Clientes.Longitude,\n"
                        + "	@codfil = Rotas.CodFil\n"
                        + "	 from Rotas\n"
                        + "	left join escala (nolock)\n"
                        + "	   on escala.SeqRota = Rotas.Sequencia   \n"
                        + "	Left join Pessoa (nolock)\n"
                        + "	   on escala.MatrChe = Pessoa.Matr \n"
                        + "	Left join Clientes (nolock) on clientes.Codigo = @cxforte\n"
                        + "							   and Clientes.CodFil = Rotas.CodFil\n"
                        + "  \n"
                        + "	Where Rotas.Sequencia = @inSequencia;\n"
                        + "\n"
                        + "	\n"
                        + "	Set @data = Replace(Convert(Varchar,Getdate(),111),'/','-');\n"
                        + "	Set @hora = Substring(Convert(Varchar,Getdate(),108),1,5);\n"
                        + "\n"
                        + "	if(@codpessoa > 0) begin\n"
                        + "		Select @seqRastrear = Isnull(Max(Sequencia),0)+1 from RastrearStat;\n"
                        + "		Insert into RastrearStat (Sequencia,CodFil, SeqRota,CodPessoa,DataPOS,HoraPOS,Data,Hora)\n"
                        + "		values(@seqRastrear,@codfil, @inSequencia,@codpessoa,@data, @hora,@data, @hora);\n"
                        + "\n"
                        + "		Insert into Rastrear (Codigo,Latitude, Longitude, Data, Hora, DtTrans, Satelite, Matr)\n"
                        + "		Values(@seqRastrear,@latitude, @longitude, @data, @hora, @data, 'Ger-SATMOB', @codpessoa);\n"
                        + "	end\n"
                        + "end\n";
            }

            sql += " SELECT \n"
                    + " Atendimentos.Qtde Atendimento, \n"
                    + " escala.rota, \n"
                    + " case when funcion.nome is null then funcionMot.Nome else funcion.nome end nome,  \n"
                    + " escala.veiculo, \n"
                    + " veiculos.placa, \n"
                    + " VeiculosMod.Descricao modeloVeic, \n"
                    + " funcion.nome nomeChe, \n"
                    + " funcionMot.nome nomeMot, \n"
                    + " rastrear.latitude, \n"
                    + " rastrear.longitude, \n"
                    + " rastrear.data, \n"
                    + " rastrear.hora,  \n"
                    + " escala.hora1, \n"
                    + " escala.hora2, \n"
                    + " escala.hora3, \n"
                    + " escala.hora4,   \n"
                    + " y.entOk, \n"
                    + " y.entPd, \n"
                    + " y.entGuias, \n"
                    + " y.entLacres, \n"
                    + " y.entValor, \n"
                    + " y.entPdGuias, \n"
                    + " y.entPdValor, \n"
                    + " y.recOk, \n"
                    + " y.recPd, \n"
                    + " y.recGuias, \n"
                    + " y.recLacres, \n"
                    + " y.recValor, \n"
                    + " y.entPdLacres, \n"
                    + " y.ValorRecepCXF \n"
                    + " from rastrear (nolock) \n"
                    + " left join rastrearstat (nolock) \n"
                    + "   on rastrear.codigo = rastrearstat.sequencia  \n"
                    + " left join Pessoa (nolock)\n"
                    + "   on rastrearstat.CodPessoa = Pessoa.Codigo\n"
                    + " left join escala (nolock)\n"
                    + "   on escala.Data = RastrearStat.Data\n"
                    + "  and (escala.MatrChe = Pessoa.Matr OR escala.MatrMot = Pessoa.Matr) \n"
                    /*+ " join (Select \n"
                    + "       Max(Codigo) Codigo, \n"
                    + "       Matr, \n"
                    + "       Data \n"
                    + "       from rastrear (nolock)     \n"
                    + "       Group by Matr, Data) ultimaComunicacao\n"
                    + "   on ultimaComunicacao.Codigo = Rastrear.Codigo \n"
                    + "  AND Escala.Data = ultimaComunicacao.Data     \n"*/
                    + " JOIN (Select Max(Sequencia) Sequencia, SeqRota\n"
                    + "       from RastrearStat (nolock)\n"
                    + "       where RastrearStat.Data = (SELECT Data FROM Rotas WHERE Sequencia = @inSequencia) \n"
                    + "       Group by RastrearStat.SeqRota) ultimaComunicacaoStat   on Rastrear.Codigo = ultimaComunicacaoStat.Sequencia"
                    + " left join funcion (nolock)\n"
                    + "   on (funcion.matr = escala.matrche OR funcion.matr = escala.matrMot)\n"
                    + "  and funcion.codfil = escala.codfil  \n"
                    + " left join funcion funcionMot (nolock) \n"
                    + "   on funcionMot.matr = escala.matrmot                   \n"
                    + "  and funcionMot.codfil = escala.codfil  \n"
                    + " left join veiculos (nolock)\n"
                    + "   on veiculos.numero = escala.veiculo \n"
                    + " left join VeiculosMod (nolock)\n"
                    + "   on VeiculosMod.Codigo = Veiculos.Modelo \n"
                    + " left join (Select\n"
                    + "             Sequencia, \n"
                    + "             sum(entOk) entOk, \n"
                    + "             sum(entPD) entPd, \n"
                    + "             sum(entGuias) entGuias, \n"
                    + "             sum(entLacres) entLacres, \n"
                    + "             sum(entValor) entValor, \n"
                    + "             sum(entPdGuias) entPdGuias, \n"
                    + "             sum(entPdValor) entPdValor, \n"
                    + "             sum(recOk) recOk, \n"
                    + "             sum(recPd) recPd, \n"
                    + "             sum(recguias) recGuias,\n"
                    + "             sum(recLacres) recLacres, \n"
                    + "             sum(recValor) recValor, \n"
                    + "             Sum(entPdLacres) entPdLacres, \n"
                    + "             (isnull(ValorRecepCXF,0)) ValorRecepCXF\n"
                    + "             From (Select \n"
                    + "                   Rt_Perc.Sequencia, Rt_Perc.Parada,\n"
                    + "                   Case when Rt_Perc.ER = 'E' and len(isnull(Rt_Perc.HrCheg,'')) > 0 then count(Distinct Rt_Perc.Parada) else 0 end entOk,\n"
                    + "                   Case when Rt_Perc.ER = 'E' and len(isnull(Rt_Perc.HrCheg,'')) = 0 then count(Distinct Rt_Perc.Parada) else 0 end entPd,\n"
                    + "                   Case when Rt_Perc.ER = 'E' and len(isnull(Rt_Perc.HrCheg,'')) > 0 then isnull(count(Distinct Rt_Guias.Guia),0) else 0 end entGuias,\n"
                    + "                   Case when Rt_Perc.ER = 'E' and len(isnull(Rt_Perc.HrCheg,'')) > 0 then isnull(count(Distinct CxfGuiasVol.Lacre),0) else 0 end entLacres,\n"
                    + "                   Case when Rt_Perc.ER = 'E' and len(isnull(Rt_Perc.HrCheg,'')) > 0 then ((isnull(sum(Distinct Rt_Guias.Valor),0))/(select top 01 isnull(Round(Valor,5),1) \n"
                    + "                    																												 from tesmoedasvlr \n"
                    + "                    																												 where tesmoedasvlr.CodFil = Rotas.CodFil \n"
                    + "																																		and Convert(Date, DtCotacao) <= Convert(Date,Getdate()-1) \n"
                    + "                    																												   and CodMoeda = (Case when Rt_GuiasMoeda.Moeda is not null then Rt_GuiasMoeda.Moeda \n"
                    + "																																		when Pedido.TipoMoeda is not null then Pedido.TipoMoeda\n"
                    + "																																		else MoedaPdrMobile end) Order by DtCotacao Desc)) else 0 end entValor, \n"
                    + "                   Case when Rt_Perc.ER = 'E' and len(isnull(Rt_Perc.HrCheg,'')) = 0 then (Select \n"
                    + "                                                                                           isnull(Count(Distinct CxfGuias.Guia),0) \n"
                    + "                                                                                           from CxfGuias (nolock) \n"
                    + "                                                                                           where CxfGuias.SeqRotaSai = Rt_Perc.Sequencia \n"
                    + "                                                                                           and CxfGuias.Hora1D = Rt_Perc.Hora1) else 0 end entPdGuias,\n"
                    + "                   Case when Rt_Perc.ER = 'E' and len(isnull(Rt_Perc.HrCheg,'')) = 0 then  (\n"
                    + "                    Select Sum(xy.Valor) from (\n"
                    + "  Select ((isnull((CxfGuias.Valor),0)) / \n"
                    + "                    	(select top 01 isnull(Round(Valor,5),1) \n"
                    + "                    	 from tesmoedasvlr \n"
                    + "                    	 where tesmoedasvlr.CodFil = Rotas.CodFil \n"
                    + "                            and Convert(Date, DtCotacao) <= Convert(Date,Getdate()-1) \n"
                    + "                    	   and CodMoeda = (Case when xy.Moeda is not null then xy.Moeda \n"
                    + "												when Pedido.TipoMoeda is not null then Pedido.TipoMoeda\n"
                    + "											else MoedaPdrMobile end) Order by DtCotacao Desc)) Valor\n"
                    + "	from CxfGuias (Nolock)                    \n"
                    + "	Left Join Rt_guias (NoLock)  on  Rt_Guias.Guia   = CxfGuias.Guia\n"
                    + "                    				and	Rt_Guias.Serie = CxfGuias.Serie\n"
                    + "		Left join Pedido (NoLock)  on Pedido.Numero = Rt_Perc.Pedido\n"
                    + "                    	and Pedido.CodFil = Rotas.CodFil\n"
                    + "		Left join Paramet (NoLock)  on Paramet.Filial_PDR = Rotas.CodFil\n"
                    + "		Left join Rt_GuiasMoeda xy (Nolock) on xy.Sequencia = Rt_Guias.Sequencia\n"
                    + "                                and xy.Parada = Rt_Guias.Parada\n"
                    + "								and xy.Guia = Rt_Guias.Guia\n"
                    + "								and xy.Serie = Rt_Guias.Serie\n"
                    + "		where CxfGuias.SeqRotaSai = Rt_Perc.Sequencia \n"
                    + "			and CxfGuias.Hora1D = Rt_Perc.Hora1) xy) else 0 end entPdValor,\n"
                    + "                   Case when Rt_Perc.ER = 'E' and len(isnull(Rt_Perc.HrCheg,'')) = 0 then isnull(count(Distinct CxfGuiasVol.Lacre),0) else 0 end entPdLacres, \n"
                    + "                   Case when Rt_Perc.ER = 'R' and len(isnull(Rt_Perc.HrCheg,'')) > 0 then count(Distinct Rt_Perc.Parada) else 0 end recOk,\n"
                    + "                   Case when Rt_Perc.ER = 'R' and len(isnull(Rt_Perc.HrCheg,'')) = 0 then count(Distinct Rt_Perc.Parada) else 0 end recPd,\n"
                    + "                   Case when Rt_Perc.ER = 'R' and len(isnull(Rt_Perc.HrCheg,'')) > 0 then count(Distinct Rt_Guias.Guia) else 0 end recGuias,\n"
                    + "                   Case when Rt_Perc.ER = 'R' and len(isnull(Rt_Perc.HrCheg,'')) > 0 then isnull(count(Distinct CxfGuiasVol.Lacre),0) else 0 end recLacres,\n"
                    + "                   Case when Rt_Perc.ER = 'R' and len(isnull(Rt_Perc.HrCheg,'')) > 0 then ((isnull(sum(Distinct Rt_Guias.Valor),0)) / (select top 01 isnull(Round(Valor,5),1) \n"
                    + "	 from tesmoedasvlr \n"
                    + "	 where CodFil = Rotas.CodFil \n"
                    + "    and Convert(Date, DtCotacao) <= Convert(Date,Getdate()-1) \n"
                    + "	   and CodMoeda = (Case when Rt_GuiasMoeda.Moeda is not null then Rt_GuiasMoeda.Moeda \n"
                    + "				when Pedido.TipoMoeda is not null then Pedido.TipoMoeda\n"
                    + "				else MoedaPdrMobile end) Order by DtCotacao Desc)) else 0 end recValor,\n"
                    + "(Select Sum(Valor) from (\n"
                    + "  Select ((Isnull(CxfGuias.Valor,0)) / \n"
                    + "                    	(select top 01 isnull(Round(Valor,5),1) \n"
                    + "                    	 from tesmoedasvlr \n"
                    + "                    	 where tesmoedasvlr.CodFil = y.codfil\n"
                    + "                            and Convert(Date, DtCotacao) <= Convert(Date,Getdate()-1) \n"
                    + "                    	   and CodMoeda = (Case when xy.Moeda is not null then xy.Moeda \n"
                    + "												when Pedido.TipoMoeda is not null then Pedido.TipoMoeda\n"
                    + "											else MoedaPdrMobile end) Order by DtCotacao Desc)) Valor,  CxfGuias.Guia, CxfGuias.Serie\n"
                    + "   From Rotas y\n"
                    + "   Left Join Rt_guias (NoLock)  on  Rt_Guias.Sequencia   = y.Sequencia\n"
                    + "                      and y.CodFil = y.codfil\n"
                    + "   Left join Rt_Perc x (NoLock) on x.Sequencia = Rt_Guias.Sequencia\n"
                    + "                    	                               and x.Parada = Rt_Guias.Parada \n"
                    + "   Left join Pedido (NoLock)  on Pedido.Numero = x.Pedido\n"
                    + "                    	                          and Pedido.CodFil = y.codfil\n"
                    + "   Left join Paramet (NoLock)  on Paramet.Filial_PDR = y.codfil\n"
                    + "   Left Join CxfGuias (NoLock)  on  CxfGuias.Guia    = Rt_Guias.Guia  \n"
                    + "                      and CxfGuias.Serie    = Rt_Guias.SerieAnt\n"
                    + "                      and CxfGuias.RotaEnt  = y.Rota \n"
                    + "                      and CXFGuias.Hora1    = x.Hora1 \n"
                    + "                      and CxfGuias.DtEnt = y.Data\n"
                    + "  Left join Rt_GuiasMoeda xy (Nolock) on xy.Sequencia = Rt_Guias.Sequencia\n"
                    + "                                   and xy.Parada = Rt_Guias.Parada\n"
                    + "								   and xy.Guia = Rt_Guias.Guia\n"
                    + "								   and xy.Serie = Rt_Guias.Serie\n"
                    + "  where y.Sequencia = @inSequencia \n"
                    + "    and CxfGuias.DtEnt is not Null) z	)	ValorRecepCXF		\n"
                    + "                   From Rt_Perc \n"
                    + "                   Left join Rotas (nolock)  \n"
                    + "                     on Rotas.Sequencia = Rt_Perc.Sequencia \n"
                    + "                   Left join Rt_Guias (nolock)  \n"
                    + "                     on Rt_Guias.Sequencia = Rt_Perc.Sequencia\n"
                    + "                    and Rt_Guias.Parada = Rt_Perc.Parada\n"
                    + "                   Left join CxfGuiasVol (nolock) on CxfGuiasVol.Guia = Rt_Guias.Guia\n"
                    + "                     and CxfGuiasVol.Serie = Rt_Guias.Serie\n"
                    + "				Left join Pedido (NoLock)  on Pedido.Numero = Rt_Perc.Pedido\n"
                    + "                    	                          and Pedido.CodFil = Rotas.CodFil\n"
                    + "				Left join Rt_GuiasMoeda (Nolock) on Rt_GuiasMoeda.Sequencia = Rt_Guias.Sequencia\n"
                    + "												and Rt_GuiasMoeda.Parada = Rt_Guias.Parada\n"
                    + "												and Rt_GuiasMoeda.Guia = Rt_Guias.Guia\n"
                    + "												and Rt_GuiasMoeda.Serie = Rt_Guias.Serie\n"
                    + "				Left join Paramet (NoLock)  on Paramet.Filial_PDR = Rotas.CodFil\n"
                    + "                 Where Rt_Perc.Sequencia = @inSequencia\n"
                    + "                   and Rt_Perc.CodCli1 not in (Select CodCli from CxForte where CxForte.CodFil = Rotas.CodFil) \n"
                    + "                   and Rt_Perc.Flag_Excl <> '*'\n"
                    + "                 Group by Rt_Perc.ER, Rt_Perc.HrCheg, Rt_Perc.Parada, Rt_Perc.Sequencia, Rt_Perc.Parada, Rt_Perc.Hora1, Rotas.CodFil, Rt_GuiasMoeda.Moeda, Pedido.TipoMoeda, Paramet.MoedaPdrMobile, Rt_Perc.Pedido, Rotas.Sequencia) z\n"
                    + "             Group by z.Sequencia, z.ValorRecepCXF) y  \n"
                    + "     on y.Sequencia = Escala.SeqRota  \n"
                    + " LEFT JOIN (SELECT\n"
                    + "            COUNT(RT_PercSLA.Parada) Qtde,\n"
                    + "            Sequencia\n"
                    + "            FROM Rt_PercSLA\n"
                    + "            WHERE Rt_PercSLA.Sequencia = @inSequencia \n"
                    + "            and len(isnull(Rt_PercSLA.HrChegVei,'')) > 0 \n"
                    + "            and len(isnull(Rt_PercSla.HrSaidaVei,'')) = 0\n"
                    + "            GROUP BY Sequencia) AS Atendimentos\n"
                    + "   ON escala.SeqRota = Atendimentos.Sequencia\n"
                    + " WHERE rastrearstat.seqrota = @inSequencia  \n"
                    + " ORDER BY rastrear.dttrans desc, rastrear.hrtrans desc\n";
            Consulta consulta = new Consulta(sql, persistencia);
            //consulta.setString(codFil);
            consulta.setString(seqRota);
            consulta.select();

            Rastrear retorno = new Rastrear();

            while (consulta.Proximo()) {
                retorno.setRota(consulta.getString("rota"));
                retorno.setAtendimento(consulta.getString("Atendimento"));
                retorno.setNome(consulta.getString("nomeChe"));
                retorno.setNomeMotorista(consulta.getString("nomeMot"));
                retorno.setVeiculo(consulta.getString("veiculo"));
                retorno.setPlaca(consulta.getString("placa"));
                retorno.setModeloVeiculo(consulta.getString("modeloVeic"));
                retorno.setData(consulta.getLocalDate("data"));
                retorno.setHora(consulta.getString("hora"));
                retorno.setLatitude(consulta.getString("latitude"));
                retorno.setLongitude(consulta.getString("longitude"));
                retorno.setHora1(consulta.getString("hora1"));
                retorno.setHora2(consulta.getString("hora2"));
                retorno.setHora3(consulta.getString("hora3"));
                retorno.setHora4(consulta.getString("hora4"));

                retorno.setEntOk(consulta.getString("entOk"));
                retorno.setEntPd(consulta.getString("entPd"));
                retorno.setEntGuias(consulta.getString("entGuias"));
                retorno.setEntLacres(consulta.getString("entLacres"));
                retorno.setEntValor(consulta.getString("entValor"));
                retorno.setEntPdGuias(consulta.getString("entPdGuias"));
                retorno.setEntPdValor(consulta.getString("entPdValor"));
                retorno.setEntPdLacres(consulta.getString("entPdLacres"));
                retorno.setRecOk(consulta.getString("recOk"));
                retorno.setRecPd(consulta.getString("recPd"));
                retorno.setRecGuias(consulta.getString("recGuias"));
                retorno.setRecLacres(consulta.getString("recLacres"));
                retorno.setRecValor(consulta.getString("recValor"));
                retorno.setVlrEntDir("0");

                retorno.setCxfEntValor(consulta.getString("ValorRecepCXF"));
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar localização - " + e.getMessage());
        }
    }

    public void inserirPosicoesSemMatr(String codFil, String data, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            sql = "  INSERT INTO Rastrear(Codigo, Latitude, Longitude, Data, Hora, DtTRans, HrTrans, Matr)\n"
                    + "  SELECT \n"
                    + "  ISNULL((SELECT MAX(Codigo) + X.Contador FROM Rastrear), 1),\n"
                    + "  X.Latitude,\n"
                    + "  X.Longitude,\n"
                    + "  X.Data,\n"
                    + "  X.Hora1,\n"
                    + "  X.Data,\n"
                    + "  X.Hora1,\n"
                    + "  COALESCE((Select TOP 1 Pessoa.Codigo from Escala\n"
                    + "    left join Pessoa on Pessoa.Matr = escala.MatrChe\n"
                    + "    where Escala.Rota = X.Rota\n"
                    + "      and Escala.CodFil = X.CodFil\n"
                    + "      and Escala.Data   = X.Data) ,\n"
                    + "      (\n"
                    + "      SELECT Tb.Codigo FROM(select \n"
                    + "                            Pessoa.Codigo,\n"
                    + "                            ROW_NUMBER() OVER(ORDER BY Pessoa.Codigo ASC) AS Cont\n"
                    + "                            from pessoa\n"
                    + "                            left join Funcion\n"
                    + "                              on Funcion.Matr = Pessoa.Matr\n"
                    + "                            where Funcion.CodFil   = X.CodFil\n"
                    + "                              and Funcion.Funcao   = 'C'\n"
                    + "                              and Funcion.Situacao = 'A') AS Tb\n"
                    + "      WHERE Tb.Cont = X.Contador)) AS Matr\n"
                    + "  FROM(\n"
                    + "  Select \n"
                    + "  Rotas.Rota,\n"
                    + "  Rotas.CodFil,\n"
                    + "  ROW_NUMBER() OVER(ORDER BY Rotas.Rota ASC) AS Contador,\n"
                    + "  (Select top 1 Clientes.Latitude from Rt_Perc\n"
                    + "    left join Clientes on Clientes.Codigo = rt_Perc.CodCli1\n"
                    + "                       and Clientes.CodFil = Rt_perc.CodFil\n"
                    + "    where Rt_Perc.Sequencia = Rotas.Sequencia \n"
                    + "      and Clientes.Latitude is not null\n"
                    + "    order by Hora1) Latitude,\n"
                    + "  (Select top 1 Clientes.Longitude from Rt_Perc\n"
                    + "    left join Clientes on Clientes.Codigo = rt_Perc.CodCli1\n"
                    + "                       and Clientes.CodFil = Rt_perc.CodFil\n"
                    + "    where Rt_Perc.Sequencia = Rotas.Sequencia \n"
                    + "      and Clientes.Latitude is not null\n"
                    + "    order by Hora1) Longitude,\n"
                    + "    Rotas.Data,   \n"
                    + "  (Select top 1 Rt_Perc.Hora1 from Rt_Perc\n"
                    + "    left join Clientes on Clientes.Codigo = rt_Perc.CodCli1\n"
                    + "                       and Clientes.CodFil = Rt_perc.CodFil\n"
                    + "    where Rt_Perc.Sequencia = Rotas.Sequencia \n"
                    + "      and Clientes.Latitude is not null\n"
                    + "    order by Hora1) Hora1\n"
                    + "    from Rotas\n"
                    + "    where Rotas.Data   = ?\n"
                    + "      and rotas.CodFil = ?\n"
                    + "      and (Rotas.TpVeic = 'F' or Rotas.TpVeic = 'L')) AS X WHERE X.Latitude IS NOT NULL AND X.Longitude IS NOT NULL;\n"
                    + "	\n"
                    + "	  \n"
                    + "	  \n"
                    + "  INSERT INTO rastrearStat(Sequencia, CodFil, SeqRota, CodPessoa, DataPos, HoraPos, Data, Hora, Placa)\n"
                    + "  SELECT\n"
                    + "  ISNULL((SELECT MAX(Sequencia) + X.Contador FROM rastrearStat), 1),\n"
                    + "  X.CodFil,\n"
                    + "  X.Sequencia,\n"
                    + "  COALESCE((Select TOP 1 Pessoa.Codigo from Escala\n"
                    + "    left join Pessoa on Pessoa.Matr = escala.MatrChe\n"
                    + "    where Escala.Rota = X.Rota\n"
                    + "      and Escala.CodFil = X.CodFil\n"
                    + "      and Escala.Data   = X.Data) ,\n"
                    + "      (\n"
                    + "      SELECT Tb.Codigo FROM(select \n"
                    + "                            Pessoa.Codigo,\n"
                    + "                            ROW_NUMBER() OVER(ORDER BY Pessoa.Codigo ASC) AS Cont\n"
                    + "                            from pessoa\n"
                    + "                            left join Funcion\n"
                    + "                              on Funcion.Matr = Pessoa.Matr\n"
                    + "                            where Funcion.CodFil   = X.CodFil\n"
                    + "                              and Funcion.Funcao   = 'C'\n"
                    + "                              and Funcion.Situacao = 'A') AS Tb\n"
                    + "      WHERE Tb.Cont = X.Contador)),\n"
                    + "  X.Data,\n"
                    + "  X.Hora1,\n"
                    + "  X.Data,\n"
                    + "  X.Hora1,\n"
                    + "  X.Placa\n"
                    + "  FROM(\n"
                    + "  Select \n"
                    + "  Rotas.Rota,\n"
                    + "  ROW_NUMBER() OVER(ORDER BY Rotas.Rota ASC) AS Contador,\n"
                    + "  rotas.CodFil,\n"
                    + "    Rotas.Sequencia , \n"
                    + "    Rotas.Data,   \n"
                    + "  (Select top 1 Rt_Perc.Hora1 from Rt_Perc\n"
                    + "    left join Clientes on Clientes.Codigo = rt_Perc.CodCli1\n"
                    + "                       and Clientes.CodFil = Rt_perc.CodFil\n"
                    + "    where Rt_Perc.Sequencia = Rotas.Sequencia \n"
                    + "      and Clientes.Latitude is not null\n"
                    + "    order by Hora1) Hora1, \n"
                    + "    (Select Veiculos.Placa from Escala\n"
                    + "    left join Veiculos on Veiculos.Numero = Escala.Veiculo\n"
                    + "    where Escala.Rota = Rotas.Rota\n"
                    + "      and Escala.CodFil = Rotas.CodFil\n"
                    + "      and Escala.Data   = Rotas.Data) Placa\n"
                    + "    from Rotas\n"
                    + "    where Rotas.Data   = ?\n"
                    + "      and rotas.CodFil = ?\n"
                    + "      and (Rotas.TpVeic = 'F' or Rotas.TpVeic = 'L')) AS X;";

            Consulta consulta = new Consulta(sql, persistencia);
            
            consulta.setString(data);
            consulta.setString(codFil);

            consulta.setString(data);
            consulta.setString(codFil);

            consulta.insert();
            consulta.close();

        } catch (Exception e) {
            throw new Exception("Falha ao inserir localização - " + e.getMessage());
        }
    }

    /**
     * Lista todas as rotas do dia por filial e a última posição dela.
     *
     * @param data
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Rastrear> buscarUltimaPosicaoRotas(String data, String codFil, Persistencia persistencia) throws Exception {
        List<Rastrear> retorno = new ArrayList<>();

        try {
            String sql = "";
            // DEsativado correcao abaixo que deixa o sistema lento Carlos 22/11/2022
            // INÍCIO - Corrreção Hora1 quando possuir menos que 4 caracteres, Ex 800, quando deveria ser 0800
            /*
            String sql = "UPDATE Rt_Perc\n"
                    + " SET Rt_Perc.Hora1 = REPLICATE('0', 4 - LEN(Rt_Perc.Hora1)) + RTrim(Rt_Perc.Hora1)\n"
                    + " WHERE LEN(Rt_Perc.Hora1) < 4";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.update();
            // FIM - Corrreção Hora1
            */

            sql = "Select \n"
                    + "(Select count(*) from Rt_PercSLA where Rt_PercSLA.Sequencia = escala.SeqRota and len(isnull(Rt_PercSLA.HrChegVei,'')) > 0 \n"
                    + "    and len(isnull(Rt_PercSla.HrSaidaVei,'')) = 0 ) Atendimento, COALESCE(escala.rota, y.Rota) rota,  escala.codfil, MaxLocationRef.hora, \n"
                    + " case when funcion.nome is null then funcionMot.Nome else funcion.nome end nome, \n"
                    + " escala.veiculo, COALESCE(rastrearStat.SeqRota, escala.SeqRota) AS SeqRota, veiculos.placa,\n"
                    + " VeiculosMod.Descricao modeloVeic, funcion.nome nomeChe, funcionMot.nome nomeMot,\n"
                    + " MaxLocationRef.latitude, MaxLocationRef.longitude, MaxLocationRef.data, escala.hora1, escala.hora2, escala.hora3, escala.hora4 ,\n"
                    + " isnull(y.entOk,0) entOk, isnull(y.entPd,0) entPd, isnull(y.entGuias,0) entGuias, isnull(y.entLacres,0) entLacres, \n"
                    + " isnull(y.entValor,0) entValor,isnull(y.entPdGuias,0) entPdGuias, isnull(y.entPdValor,0) entPdValor, isnull(y.recOk,0) recOk, \n"
                    + " isnull(y.recPd,0) recPd, isnull(y.recGuias,0) recGuias, isnull(y.recLacres,0) recLacres, isnull(y.recValor,0) recValor, \n"
                    + " isnull(y.entPdLacres,0) entPdLacres, isnull(y.ValorRecepCXF,0) ValorRecepCXF,\n"
                    + " isnull(y.ValorEntDir,0) ValorEntDir, isnull(y.ServAtrasados,0) ServAtrasados, \n"
                    + "     isnull(y.SrvEfetivos,0) SrvEfetivos, isnull(y.SrvAdiantados,0) SrvAdiantados, \n"
                    + "     isnull(y.SrvAtrasados,0) SrvAtrasados, isnull(y.SrvPendentes,0) SrvPendentes     \n"
                    //+ " from rastrear (Nolock)\n"
                    + " from rastrearStat (Nolock)\n"
                    /*+ " left join rastrearStat (Nolock) on rastrear.codigo = rastrearstat.sequencia\n"
                    + "	                        and rastrear.Matr = rastrearstat.codpessoa\n"*/
                    + " left join Pessoa  on Pessoa.Codigo = rastrearstat.CodPessoa \n"
                    + " left join escala  on escala.Data = RastrearStat.Data\n"
                    + "                   and escala.SeqRota = rastrearStat.SeqRota\n"
                    + "                   and ((escala.matrche = Pessoa.Matr)\n"
                    + "                     or (escala.matrMot = Pessoa.Matr)) \n"
                    + "  JOIN (SELECT\n"
                    + "       MAX(RastrearStat.Sequencia) Sequencia,\n"
                    + "       RastrearStat.SeqRota\n"
                    + "       FROM RastrearStat (nolock)\n"
                    + "       Left JOIN Rotas RotasUlt\n"
                    + "         ON RastrearStat.SeqRota = RotasUlt.Sequencia\n"
                    + "        AND RastrearStat.Data    = RotasUlt.Data\n"
                    + "       Left join Escala on Escala.SeqRota = RotasUlt.Sequencia\n"
                    + "       Left join Pessoa PessoaChe on PessoaChe.Matr = Escala.MatrChe\n "
                    + "       Left join Pessoa PessoaMot on PessoaMot.Matr = Escala.MatrMot\n "                    
                    + "       WHERE RastrearStat.Data = ? \n"
                    + "         and RastrearStat.CodPessoa = Case when Pessoache.codigo is not null then PessoaChe.Codigo else PessoaMot.Codigo end "
                    + "         and SubString(RastrearStat.Hora,3,1) = ':' "
                    + "       GROUP BY RastrearStat.SeqRota, RastrearStat.codPessoa) ultimaComunicacaoStat   \n"
                    + "  ON rastrearStat.Sequencia = ultimaComunicacaoStat.Sequencia \n"
                    + "  AND rastrearStat.SeqRota   = ultimaComunicacaoStat.SeqRota\n"
                    + "  left JOIN rastrear \n"
                    + "  ON  rastrear.Codigo = rastrearStat.Sequencia \n"
                    + "  JOIN (SELECT MAX(X.Codigo) Codigo, X.Matr, X.Data\n"
                    + "       FROM Rastrear X\n"
                    + " Left join RastrearStat X2 on X2.Sequencia = X.Codigo\n"
                    + " Left join Escala on Escala.SeqRota = X2.SeqRota\n"
                    + " Left join Pessoa PChe on PChe.Matr = Escala.MatrChe\n"
                    + " Left join Pessoa PMot on PMot.Matr = Escala.MatrMot\n"                
                    + "       WHERE X.Data = ? \n"
                    + "         and X2.CodPessoa = Case when PChe.Codigo is not null then PChe.Codigo else PMot.Codigo End \n"
                    + "         and SubString(X2.Hora,3,1) = ':'\n"
                    + "       GROUP BY X.Matr, X.Data) AS MaxLocation \n"
                    + "  ON rastrear.Matr = MaxLocation.Matr\n"
                    + " AND rastrear.Data = MaxLocation.Data\n"
                    + " JOIN rastrear MaxLocationRef\n"
                    + "  ON MaxLocation.Codigo = MaxLocationRef.Codigo\n"
                    + " Left join funcion on funcion.matr = escala.matrche\n"
                    + "                   and funcion.codfil = escala.codfil  \n"
                    + " left join funcion funcionMot  on funcionMot.matr = escala.matrmot \n"
                    + "                              and funcionMot.codfil = escala.codfil \n"
                    + " left join veiculos  on veiculos.numero = escala.veiculo\n"
                    + " left join VeiculosMod  on VeiculosMod.Codigo = Veiculos.Modelo      \n"
                    + " left join (Select Rota, CodFil, Sequencia, sum(entOk) entOk, sum(entPD) entPd, sum(entGuias) entGuias, sum(entLacres) entLacres, sum(entValor) entValor,\n"
                    + "    sum(entPdGuias) entPdGuias, sum(entPdValor) entPdValor, sum(recOk) recOk, sum(recPd) recPd, sum(recguias) recGuias, sum(recLacres) recLacres,\n"
                    + "    sum(recValor) recValor, Sum(entPdLacres) entPdLacres, Max(isnull(ValorRecepCXF,0)) ValorRecepCXF, sum(isnull(ValorEntDir,0)) ValorEntDir, \n"
                    + "    sum(isnull(ServAtrasados,0)) ServAtrasados, \n"
                    + "    Sum(isnull(SrvEfetivos,0))SrvEfetivos , Sum(isnull(SrvAdiantados,0)) SrvAdiantados, Sum(isnull(SrvAtrasados,0)) SrvAtrasados, Sum(isnull(SrvPendentes,0)) SrvPendentes \n"
                    + "From (\n"
                    + "Select\n"
                    + "Rotas.CodFil,\n"
                    + "Rotas.Rota,\n"
                    + "Rt_Perc.Sequencia, Rt_Perc.Parada,\n"
                    + "Case when Rt_Perc.ER = 'E' and len(isnull(Rt_Perc.HrCheg,'')) > 0 then count(Distinct Rt_Perc.Parada) else 0 end entOk,\n"
                    + "Case when Rt_Perc.ER = 'E' and len(isnull(Rt_Perc.HrCheg,'')) = 0 then count(Distinct Rt_Perc.Parada) else 0 end entPd,\n"
                    + "Case when Rt_Perc.ER = 'E' and len(isnull(Rt_Perc.HrCheg,'')) > 0 then isnull(count(Distinct Rt_Guias.Guia),0) else 0 end entGuias,\n"
                    + "Case when Rt_Perc.ER = 'E' and len(isnull(Rt_Perc.HrCheg,'')) > 0 then isnull(count(Distinct CxfGuiasVol.Lacre),0) else 0 end entLacres,\n"
                    + "Case when Rt_Perc.ER = 'E' and len(isnull(Rt_Perc.HrCheg,'')) > 0 then ((isnull(sum(Distinct Rt_Guias.Valor),0))) else 0 end entValor, \n"
//                    + "Case when Rt_Perc.ER = 'E' and len(isnull(Rt_Perc.HrCheg,'')) > 0 then ((isnull(sum(Distinct Rt_Guias.Valor),0))/ISNULL((select top 01 isnull(Round(Valor,5),1) \n"
//                    + "                    	 from tesmoedasvlr \n"
//                    + "                    	 where tesmoedasvlr.CodFil = Rotas.CodFil \n"
//                    + "                            and Convert(Date, DtCotacao) <= Convert(Date,Getdate()-1) \n"
//                    + "                    	   and CodMoeda = (Case when Rt_GuiasMoeda.Moeda is not null then Rt_GuiasMoeda.Moeda \n"
//                    + "							when Pedido.TipoMoeda is not null then Pedido.TipoMoeda\n"
//                    + "							else MoedaPdrMobile end) Order by DtCotacao Desc),1)) else 0 end entValor, \n"
                    + "\n"
                    + "Case when Rt_Perc.ER = 'E' and len(isnull(Rt_Perc.HrCheg,'')) = 0 then\n"
                    + "(Select isnull(Count(Distinct CxfGuias.Guia),0) from CxfGuias \n"
                    + "    where CxfGuias.SeqRotaSai = Rt_Perc.Sequencia and CxfGuias.Hora1D = Rt_Perc.Hora1) else 0 end entPdGuias,\n"
                    + "Case when Rt_Perc.ER = 'E' and len(isnull(Rt_Perc.HrCheg,'')) = 0 then\n"
                    + " (\n"
                    + "                    Select Sum(xy.Valor) from (\n"
                    + "  Select ((isnull((CxfGuias.Valor),0))) Valor \n"
//                    + "  Select ((isnull((CxfGuias.Valor),0)) / \n"
//                    + "                    	ISNULL((select top 01 isnull(Round(Valor,5),1) \n"
//                    + "                    	 from tesmoedasvlr \n"
//                    + "                    	 where tesmoedasvlr.CodFil = Rotas.CodFil \n"
//                    + "                            and Convert(Date, DtCotacao) <= Convert(Date,Getdate()-1) \n"
//                    + "                    	   and CodMoeda = (Case when xy.Moeda is not null then xy.Moeda \n"
//                    + "												when Pedido.TipoMoeda is not null then Pedido.TipoMoeda\n"
//                    + "											else MoedaPdrMobile end) Order by DtCotacao Desc), 1)) Valor\n"
                    + "	from CxfGuias (Nolock)                    \n"
                    + "	Left Join Rt_guias (NoLock)  on  Rt_Guias.Guia   = CxfGuias.Guia\n"
                    + "                    				and	Rt_Guias.Serie = CxfGuias.Serie\n"
                    + "		Left join Pedido (NoLock)  on Pedido.Numero = Rt_Perc.Pedido\n"
                    + "                    	and Pedido.CodFil = Rotas.CodFil\n"
                    + "		Left join Paramet (NoLock)  on Paramet.Filial_PDR = Rotas.CodFil\n"
                    + "		Left join Rt_GuiasMoeda xy (Nolock) on xy.Sequencia = Rt_Guias.Sequencia\n"
                    + "                                and xy.Parada = Rt_Guias.Parada\n"
                    + "								and xy.Guia = Rt_Guias.Guia\n"
                    + "								and xy.Serie = Rt_Guias.Serie\n"
                    + "		where CxfGuias.SeqRotaSai = Rt_Perc.Sequencia \n"
                    + "			and CxfGuias.Hora1D = Rt_Perc.Hora1) xy) else 0 end entPdValor,\n"
                    + "Case when Rt_Perc.ER = 'E' and len(isnull(Rt_Perc.HrCheg,'')) = 0 then isnull(count(Distinct CxfGuiasVol.Lacre),0) else 0 end entPdLacres,\n"
                    + "Case when Rt_Perc.ER = 'R' and len(isnull(Rt_Perc.HrCheg,'')) > 0 then count(Distinct Rt_Perc.Parada) else 0 end recOk,\n"
                    + "Case when Rt_Perc.ER = 'R' and len(isnull(Rt_Perc.HrCheg,'')) = 0 then count(Distinct Rt_Perc.Parada) else 0 end recPd,\n"
                    + "Case when Rt_Perc.ER = 'R' and len(isnull(Rt_Perc.HrCheg,'')) > 0 then count(Distinct Rt_Guias.Guia) else 0 end recGuias,\n"
                    + "Case when Rt_Perc.ER = 'R' and len(isnull(Rt_Perc.HrCheg,'')) > 0 then isnull(count(Distinct CxfGuiasVol.Lacre),0) else 0 end recLacres,\n"
                    + "Case when Rt_Perc.ER = 'R' and len(isnull(Rt_Perc.HrCheg,'')) > 0 then ((isnull(sum(Distinct Rt_Guias.Valor),0))) else 0 end recValor, \n"
//                    + "Case when Rt_Perc.ER = 'R' and len(isnull(Rt_Perc.HrCheg,'')) > 0 then ((isnull(sum(Distinct Rt_Guias.Valor),0)) / ISNULL((select top 01 isnull(Round(Valor,5),1) \n"
//                    + "	 from tesmoedasvlr \n"
//                    + "	 where CodFil = Rotas.CodFil \n"
//                    + "    and Convert(Date, DtCotacao) <= Convert(Date,Getdate()-1) \n"
//                    + "	   and CodMoeda = (Case when Rt_GuiasMoeda.Moeda is not null then Rt_GuiasMoeda.Moeda \n"
//                    + "				when Pedido.TipoMoeda is not null then Pedido.TipoMoeda\n"
//                    + "				else MoedaPdrMobile end) Order by DtCotacao Desc),1)) else 0 end recValor,\n"
                    + "  (\n"
                    + "  Select sum(za.Valor) from (\n"
                    + "  Select ((Isnull(CxfGuias.Valor,0)))Valor  \n"
//                    + "  Select ((Isnull(CxfGuias.Valor,0)) / \n"
//                    + "                    	ISNULL((select top 01 isnull(Round(Valor,5),1) \n"
//                    + "                    	 from tesmoedasvlr \n"
//                    + "                    	 where tesmoedasvlr.CodFil = Rotas.CodFil\n"
//                    + "                            and Convert(Date, DtCotacao) <= Convert(Date,Getdate()-1) \n"
//                    + "                    	   and CodMoeda = (Case when xy.Moeda is not null then xy.Moeda \n"
//                    + "												when Pedido.TipoMoeda is not null then Pedido.TipoMoeda\n"
//                    + "											else MoedaPdrMobile end) Order by DtCotacao Desc), 1)) Valor,  CxfGuias.Guia, CxfGuias.Serie\n"
                    + "   From Rotas y\n"
                    + "   Left Join Rt_guias (NoLock)  on  Rt_Guias.Sequencia   = y.Sequencia\n"
                    + "                      and y.CodFil = Rotas.CodFil\n"
                    + "   Left join Rt_Perc x (NoLock) on x.Sequencia = Rt_Guias.Sequencia\n"
                    + "                    	                               and x.Parada = Rt_Guias.Parada \n"
                    + "   Left join Pedido (NoLock)  on Pedido.Numero = x.Pedido\n"
                    + "                    	                          and Pedido.CodFil = Rotas.CodFil\n"
                    + "   Left join Paramet (NoLock)  on Paramet.Filial_PDR = Rotas.CodFil\n"
                    + "   Left Join CxfGuias (NoLock)  on  CxfGuias.Guia    = Rt_Guias.Guia  \n"
                    + "                      and CxfGuias.Serie    = Rt_Guias.SerieAnt\n"
                    + "                      and CxfGuias.RotaEnt  = y.Rota \n"
                    + "                      and CXFGuias.Hora1    = x.Hora1 \n"
                    + "                      and CxfGuias.DtEnt = y.Data\n"
                    + "  Left join Rt_GuiasMoeda xy (Nolock) on xy.Sequencia = Rt_Guias.Sequencia\n"
                    + "                                   and xy.Parada = Rt_Guias.Parada\n"
                    + "								   and xy.Guia = Rt_Guias.Guia\n"
                    + "								   and xy.Serie = Rt_Guias.Serie\n"
                    + "  where y.Sequencia = Rotas.Sequencia\n"
                    + "    and CxfGuias.DtEnt is not Null ) za\n"
                    + "  ) ValorRecepCXF, \n"
                    + "   (Select sum(zy.Valor) from (\n"
                    + "                    	Select ((isnull(b.Valor,0))) Valor  \n"
//                    + "                    	Select ((isnull(b.Valor,0)) / \n"
//                    + "                    	ISNULL((select top 01 isnull(Round(Valor,5),1) \n"
//                    + "                    	 from tesmoedasvlr \n"
//                    + "                    	 where tesmoedasvlr.CodFil = Rotas.CodFil \n"
//                    + "                            and Convert(Date, DtCotacao) <= Convert(Date,Getdate()-1) \n"
//                    + "                    	   and CodMoeda = (Case when xy.Moeda is not null then xy.Moeda \n"
//                    + "												when Pedido.TipoMoeda is not null then Pedido.TipoMoeda\n"
//                    + "											else MoedaPdrMobile end) Order by DtCotacao Desc), 1)) Valor\n"
                    + "                    	 From Rt_Guias b (noLock) \n"
                    + "    Left join Rt_Perc c (noLock) on c.Sequencia = b.Sequencia   \n"
                    + "                        and c.Parada = b.Parada  \n"
                    + "    Left join Rotas (Nolock)  on Rotas.Sequencia = Rt_Perc.Sequencia\n"
                    + "   Left join Pedido (NoLock)  on Pedido.Numero = c.Pedido\n"
                    + "                    	    and Pedido.CodFil = Rotas.Codfil\n"
                    + "   Left join Paramet (NoLock)  on Paramet.Filial_PDR = Rotas.CodFil \n"
                    + "     Left join Rt_GuiasMoeda xy (Nolock) on xy.Sequencia = b.Sequencia\n"
                    + "                                   and xy.Parada = b.Parada\n"
                    + "								   and xy.Guia = b.Guia\n"
                    + "								   and xy.Serie = b.Serie\n"
                    + "    Where b.Sequencia = Rt_Perc.Sequencia \n"
                    + "      and c.Hora1 = Rt_Perc.Hora1D \n"
                    + "      and Len(c.HrCheg) >= 5) zy\n"
                    + "  ) ValorEntDir,  \n"
                    + " (Select count(*) From Rt_Perc xa (nolock) \n"
                    + "   Where xa.Sequencia = Rt_Perc.Sequencia \n"
                    + "     and xa.Flag_Excl <> '*' \n"
                    + "	 and (len(xa.HrCheg) < 4 or xa.HrCheg is null) \n"
                    + "     and DateDiff(mi, Convert(Datetime, (Convert(varchar,Rotas.Data,112) + ' ' + Replace(Substring(xa.Hora1,1,2),':','0')+':'+Replace(Substring(xa.Hora1,3,2),':','0')+':00' )), getdate()) > 15 \n"
                    + "	 and Replace(Substring(xa.Hora1,1,2),':','0') between 00 and 23 \n"
                    + "	 and Replace(Substring(xa.Hora1,3,2),':','0') between 00 and 59 \n"
                    + ")  ServAtrasados, \n"
                    + "(Select Count(*) Qtde \n"
                    + "  from Rt_Perc a (noLock)\n"
                    + "  Left Join Rotas b (noLock) on b.Sequencia = a.Sequencia\n"
                    + "  where a.Sequencia = Rotas.Sequencia\n"
                    + "    and a.Flag_Excl <> '*'\n"
                    //+ "    and a.CodCli1 not in (Select CodCli from CxForte where CxForte.CodFil = b.CodFil)\n"
                    + "    and a.Parada = Rt_Perc.Parada\n"
                    + "    and a.HrCheg is not null \n"
                    + "    and a.HrCheg <> ''\n"
                    + "    and abs(DateDiff(mi, Case when len(a.Hora1) = 4 then Substring(a.Hora1,1,2)+':'+Substring(a.Hora1,3,2) else a.Hora1 end, a.HrCheg)) between 0 and 15) SrvEfetivos, \n"
                    + "(Select Count(*) Qtde  \n"
                    + "  from Rt_Perc a (noLock)\n"
                    + "  Left Join Rotas b (noLock) on b.Sequencia = a.Sequencia\n"
                    + "  where a.Sequencia = Rotas.Sequencia \n"
                    + "	  and a.Flag_Excl <> '*'\n"
                    //+ "	  and a.CodCli1 not in (Select CodCli from CxForte where CxForte.CodFil = b.CodFil)\n"
                    + "    and a.Parada = Rt_Perc.Parada\n"
                    + "	  and a.HrCheg is not null \n"
                    + "	  and a.HrCheg <> ''\n"
                    + "	  and DateDiff(mi, Case when len(a.Hora1) = 4 then Substring(a.Hora1,1,2)+':'+Substring(a.Hora1,3,2) else a.Hora1 end, a.HrCheg) < 0) SrvAdiantados, \n"
                    + "(Select Count(*) Qtde \n"
                    + "  from Rt_Perc a (noLock)\n"
                    + "  Left Join Rotas b (noLock) on b.Sequencia = a.Sequencia\n"
                    + "  where a.Sequencia = Rotas.Sequencia\n"
                    + "	  and a.Flag_Excl <> '*'\n"
                    //+ "	  and a.CodCli1 not in (Select CodCli from CxForte where CxForte.CodFil = b.CodFil)\n"
                    + "    and a.Parada = Rt_Perc.Parada\n"
                    + "	  and a.HrCheg is not null \n"
                    + "	  and a.HrCheg <> ''\n"
                    + "	  and DateDiff(mi, Case when len(a.Hora1) = 4 then Substring(a.Hora1,1,2)+':'+Substring(a.Hora1,3,2) else a.Hora1 end, a.HrCheg) > 15) SrvAtrasados, \n"
                    + "(Select Count(*) Qtde  \n"
                    + "from Rt_Perc a (noLock)\n"
                    + "Left Join Rotas b (noLock) on b.Sequencia = a.Sequencia\n"
                    + "where a.Sequencia = Rotas.Sequencia\n"
                    + "	and a.Flag_Excl <> '*'\n"
                    //+ "	and a.CodCli1 not in (Select CodCli from CxForte where CxForte.CodFil = b.CodFil)\n"
                    + "    and a.Parada = Rt_Perc.Parada\n"
                    + " 	and (a.HrCheg is null or a.HrCheg = '')) SrvPendentes \n"
                    + " From Rt_Perc (nolock) \n"
                    + "   Left join Rt_PercDet  on Rt_PercDet.Sequencia = Rt_Perc.Sequencia\n"
                    + "                        and Rt_PercDet.Parada = Rt_Perc.Parada\n"
                    + "                        and Rt_PercDet.CodFil = Rt_Perc.CodFil\n"
                    + "                       AND Rt_PercDet.Km > 0\n"
                    + " Left join Rotas (nolock) on Rotas.Sequencia = Rt_Perc.Sequencia\n"
                    + " Left join Rt_Guias (nolock) on Rt_Guias.Sequencia = Rt_Perc.Sequencia\n"
                    + "                    and Rt_Guias.Parada = Rt_Perc.Parada\n"
                    + " Left join CxfGuiasVol (nolock) on CxfGuiasVol.Guia = Rt_Guias.Guia\n"
                    + "                       and CxfGuiasVol.Serie = Rt_Guias.Serie\n"
                    + " Left join Pedido (NoLock)  on Pedido.Numero = Rt_Perc.Pedido\n"
                    + "	   					and Pedido.CodFil = Rotas.Codfil\n"
                    + " Left join Paramet (NoLock)  on Paramet.Filial_PDR = Rotas.CodFil	\n"
                    + "  Left join Rt_GuiasMoeda  (Nolock) on Rt_GuiasMoeda.Sequencia = Rt_Guias.Sequencia\n"
                    + "                                   and Rt_GuiasMoeda.Parada = Rt_Guias.Parada\n"
                    + "					  and Rt_GuiasMoeda.Guia = Rt_Guias.Guia\n"
                    + "					  and Rt_GuiasMoeda.Serie = Rt_Guias.Serie\n"
                    + " Where Rotas.Data = ? \n"
                    + " and Rt_Perc.Flag_Excl <> '*' \n"
                    + " and Rt_Perc.CodCli1 not in (Select CodCli from CxForte where CxForte.CodFil = Rotas.CodFil) \n"
                    + " Group by Rt_Perc.ER, Rt_Perc.HrCheg, Rt_Perc.Parada, Rt_Perc.Sequencia, Rt_Perc.Parada, Rt_Perc.Hora1, Rotas.Data, Rotas.Rota, Rotas.CodFil, Rt_Perc.Hora1D, Pedido.TipoMoeda,Paramet.MoedaPdrMobile, Rotas.Sequencia, Rt_GuiasMoeda.Moeda, Rt_Perc.Pedido) z\n"
                    + " Group by Z.Rota, z.CodFil, z.Sequencia) y  on y.Sequencia = RastrearStat.SeqRota\n"
                    /*+ " inner join (Select Max(Codigo) Codigo, Matr from rastrear (nolock)\n"
                    + " where rastrear.Data =  ? \n"
                    + " Group by Matr) ultimaComunicacao  on ultimaComunicacao.Codigo = Rastrear.Codigo\n"
                    + " INNER JOIN (SELECT\n"
                    + "             MAX(RastrearStat.Sequencia) Sequencia,\n"
                    + "             RastrearStat.SeqRota\n"
                    + "             FROM RastrearStat (nolock)\n"
                    + "             WHERE RastrearStat.Data = ? \n"
                    + "             GROUP BY RastrearStat.SeqRota) ultimaComunicacaoStat   ON ultimaComunicacao.Codigo = ultimaComunicacaoStat.Sequencia"*
                    + " where escala.Data =   ? \n";*/
                    + " WHERE RastrearStat.Data >= ? \n";

            if (!codFil.equals("")) {
                sql += " and y.codFil = ? \n";
            }
            sql += " order by COALESCE(escala.rota, y.Rota)";
            Consulta consulta;
            
            consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.setString(data);
            consulta.setString(data);
            consulta.setString(data);
            //consulta.setString(data);
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }
            consulta.select();
            Rastrear rastrear;
            while (consulta.Proximo()) {
                rastrear = new Rastrear();
                rastrear.setRota(consulta.getString("rota"));
                rastrear.setSeqRota(consulta.getString("SeqRota"));
                rastrear.setAtendimento(consulta.getString("Atendimento"));
                rastrear.setCodFil(consulta.getString("CodFil"));
                rastrear.setNome(consulta.getString("nomeChe"));
                rastrear.setNomeMotorista(consulta.getString("nomeMot"));
                rastrear.setVeiculo(consulta.getString("veiculo"));
                rastrear.setPlaca(consulta.getString("placa"));
                rastrear.setModeloVeiculo(consulta.getString("modeloVeic"));
                rastrear.setData(consulta.getLocalDate("data"));
                rastrear.setHora(consulta.getString("hora"));
                rastrear.setLatitude(consulta.getString("latitude"));
                rastrear.setLongitude(consulta.getString("longitude"));
                rastrear.setHora1(consulta.getString("hora1"));
                rastrear.setHora2(consulta.getString("hora2"));
                rastrear.setHora3(consulta.getString("hora3"));
                rastrear.setHora4(consulta.getString("hora4"));

                rastrear.setEntOk(consulta.getString("entOk"));
                rastrear.setEntPd(consulta.getString("entPd"));
                rastrear.setEntGuias(consulta.getString("entGuias"));
                rastrear.setEntLacres(consulta.getString("entLacres"));
                rastrear.setEntValor(consulta.getString("entValor"));
                rastrear.setEntPdGuias(consulta.getString("entPdGuias"));
                rastrear.setEntPdValor(consulta.getString("entPdValor"));
                rastrear.setEntPdLacres(consulta.getString("entPdLacres"));
                rastrear.setRecOk(consulta.getString("recOk"));
                rastrear.setRecPd(consulta.getString("recPd"));
                rastrear.setRecGuias(consulta.getString("recGuias"));
                rastrear.setRecLacres(consulta.getString("recLacres"));
                rastrear.setRecValor(consulta.getString("recValor"));

                rastrear.setCxfEntValor(consulta.getString("ValorRecepCXF"));
                rastrear.setVlrEntDir(consulta.getString("ValorEntDir"));
                rastrear.setServAtrasados(consulta.getString("ServAtrasados"));

                rastrear.setSrvEfetivos(consulta.getString("SrvEfetivos"));
                rastrear.setSrvAdiantados(consulta.getString("SrvAdiantados"));
                rastrear.setSrvAtrasados(consulta.getString("SrvAtrasados"));
                rastrear.setSrvPendentes(consulta.getString("SrvPendentes"));

                retorno.add(rastrear);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            if (retorno.size() > 0) {
                return retorno;
            }

            throw new Exception("RastrearDao.buscarUltimaPosicaoRotas - " + e.getMessage() + "\r\n"
                    + "Select \n"
                    + "(Select count(*) from Rt_PercSLA where Rt_PercSLA.Sequencia = escala.SeqRota and len(isnull(Rt_PercSLA.HrChegVei,'')) > 0 \n"
                    + "    and len(isnull(Rt_PercSla.HrSaidaVei,'')) = 0 ) Atendimento, escala.rota,  escala.codfil, rastrear.hora, \n"
                    + " case when funcion.nome is null then funcionMot.Nome else funcion.nome end nome, \n"
                    + " escala.veiculo, escala.SeqRota, veiculos.placa,\n"
                    + " VeiculosMod.Descricao modeloVeic, funcion.nome nomeChe, funcionMot.nome nomeMot,\n"
                    + " rastrear.latitude, rastrear.longitude, rastrear.data, escala.hora1, escala.hora2, escala.hora3, escala.hora4 ,\n"
                    + " isnull(y.entOk,0) entOk, isnull(y.entPd,0) entPd, isnull(y.entGuias,0) entGuias, isnull(y.entLacres,0) entLacres, \n"
                    + " isnull(y.entValor,0) entValor,isnull(y.entPdGuias,0) entPdGuias, isnull(y.entPdValor,0) entPdValor, isnull(y.recOk,0) recOk, \n"
                    + " isnull(y.recPd,0) recPd, isnull(y.recGuias,0) recGuias, isnull(y.recLacres,0) recLacres, isnull(y.recValor,0) recValor, \n"
                    + " isnull(y.entPdLacres,0) entPdLacres, isnull(y.ValorRecepCXF,0) ValorRecepCXF,\n"
                    + " isnull(y.ValorEntDir,0) ValorEntDir, isnull(y.ServAtrasados,0) ServAtrasados, \n"
                    + "     isnull(y.SrvEfetivos,0) SrvEfetivos, isnull(y.SrvAdiantados,0) SrvAdiantados, \n"
                    + "     isnull(y.SrvAtrasados,0) SrvAtrasados, isnull(y.SrvPendentes,0) SrvPendentes     \n"
                    + " from rastrear (Nolock)\n"
                    + " left join rastrearStat (Nolock) on rastrear.codigo = rastrearstat.sequencia\n"
                    + "	                        and rastrear.Matr = rastrearstat.codpessoa\n"
                    + " left join Pessoa  on rastrearstat.CodPessoa = Pessoa.Codigo\n"
                    + " inner join escala  on escala.Data = RastrearStat.Data\n"
                    + "                   and ((escala.matrche = Pessoa.Matr)\n"
                    + "                     or (escala.matrMot = Pessoa.Matr)) \n"
                    + " Left join funcion on funcion.matr = escala.matrche\n"
                    + "                   and funcion.codfil = escala.codfil  \n"
                    + " left join funcion funcionMot  on funcionMot.matr = escala.matrmot \n"
                    + "                              and funcionMot.codfil = escala.codfil \n"
                    + " left join veiculos  on veiculos.numero = escala.veiculo\n"
                    + " left join VeiculosMod  on VeiculosMod.Codigo = Veiculos.Modelo      \n"
                    + " left join (Select Sequencia, sum(entOk) entOk, sum(entPD) entPd, sum(entGuias) entGuias, sum(entLacres) entLacres, sum(entValor) entValor,\n"
                    + "    sum(entPdGuias) entPdGuias, sum(entPdValor) entPdValor, sum(recOk) recOk, sum(recPd) recPd, sum(recguias) recGuias, sum(recLacres) recLacres,\n"
                    + "    sum(recValor) recValor, Sum(entPdLacres) entPdLacres, Max(isnull(ValorRecepCXF,0)) ValorRecepCXF, sum(isnull(ValorEntDir,0)) ValorEntDir, \n"
                    + "    sum(isnull(ServAtrasados,0)) ServAtrasados, \n"
                    + "    Sum(isnull(SrvEfetivos,0))SrvEfetivos , Sum(isnull(SrvAdiantados,0)) SrvAdiantados, Sum(isnull(SrvAtrasados,0)) SrvAtrasados, Sum(isnull(SrvPendentes,0)) SrvPendentes \n"
                    + "From (\n"
                    + "Select\n"
                    + "Rt_Perc.Sequencia, Rt_Perc.Parada,\n"
                    + "Case when Rt_Perc.ER = 'E' and len(isnull(Rt_Perc.HrCheg,'')) > 0 then count(Distinct Rt_Perc.Parada) else 0 end entOk,\n"
                    + "Case when Rt_Perc.ER = 'E' and len(isnull(Rt_Perc.HrCheg,'')) = 0 then count(Distinct Rt_Perc.Parada) else 0 end entPd,\n"
                    + "Case when Rt_Perc.ER = 'E' and len(isnull(Rt_Perc.HrCheg,'')) > 0 then isnull(count(Distinct Rt_Guias.Guia),0) else 0 end entGuias,\n"
                    + "Case when Rt_Perc.ER = 'E' and len(isnull(Rt_Perc.HrCheg,'')) > 0 then isnull(count(Distinct CxfGuiasVol.Lacre),0) else 0 end entLacres,\n"
                    + "Case when Rt_Perc.ER = 'E' and len(isnull(Rt_Perc.HrCheg,'')) > 0 then ((isnull(sum(Distinct Rt_Guias.Valor),0))/(select top 01 isnull(Round(Valor,5),1) \n"
                    + "                    	 from tesmoedasvlr \n"
                    + "                    	 where tesmoedasvlr.CodFil = Rotas.CodFil \n"
                    + "                            and Convert(Date, DtCotacao) <= Convert(Date,Getdate()-1) \n"
                    + "                    	   and CodMoeda = (Case when Rt_GuiasMoeda.Moeda is not null then Rt_GuiasMoeda.Moeda \n"
                    + "							when Pedido.TipoMoeda is not null then Pedido.TipoMoeda\n"
                    + "							else MoedaPdrMobile end) Order by DtCotacao Desc)) else 0 end entValor, \n"
                    + "\n"
                    + "Case when Rt_Perc.ER = 'E' and len(isnull(Rt_Perc.HrCheg,'')) = 0 then\n"
                    + "(Select isnull(Count(Distinct CxfGuias.Guia),0) from CxfGuias \n"
                    + "    where CxfGuias.SeqRotaSai = Rt_Perc.Sequencia and CxfGuias.Hora1D = Rt_Perc.Hora1) else 0 end entPdGuias,\n"
                    + "Case when Rt_Perc.ER = 'E' and len(isnull(Rt_Perc.HrCheg,'')) = 0 then\n"
                    + " (\n"
                    + "                    Select Sum(xy.Valor) from (\n"
                    + "  Select ((isnull((CxfGuias.Valor),0)) / \n"
                    + "                    	(select top 01 isnull(Round(Valor,5),1) \n"
                    + "                    	 from tesmoedasvlr \n"
                    + "                    	 where tesmoedasvlr.CodFil = Rotas.CodFil \n"
                    + "                            and Convert(Date, DtCotacao) <= Convert(Date,Getdate()-1) \n"
                    + "                    	   and CodMoeda = (Case when xy.Moeda is not null then xy.Moeda \n"
                    + "												when Pedido.TipoMoeda is not null then Pedido.TipoMoeda\n"
                    + "											else MoedaPdrMobile end) Order by DtCotacao Desc)) Valor\n"
                    + "	from CxfGuias (Nolock)                    \n"
                    + "	Left Join Rt_guias (NoLock)  on  Rt_Guias.Guia   = CxfGuias.Guia\n"
                    + "                    				and	Rt_Guias.Serie = CxfGuias.Serie\n"
                    + "		Left join Pedido (NoLock)  on Pedido.Numero = Rt_Perc.Pedido\n"
                    + "                    	and Pedido.CodFil = Rotas.CodFil\n"
                    + "		Left join Paramet (NoLock)  on Paramet.Filial_PDR = Rotas.CodFil\n"
                    + "		Left join Rt_GuiasMoeda xy (Nolock) on xy.Sequencia = Rt_Guias.Sequencia\n"
                    + "                                and xy.Parada = Rt_Guias.Parada\n"
                    + "								and xy.Guia = Rt_Guias.Guia\n"
                    + "								and xy.Serie = Rt_Guias.Serie\n"
                    + "		where CxfGuias.SeqRotaSai = Rt_Perc.Sequencia \n"
                    + "			and CxfGuias.Hora1D = Rt_Perc.Hora1) xy) else 0 end entPdValor,\n"
                    + "Case when Rt_Perc.ER = 'E' and len(isnull(Rt_Perc.HrCheg,'')) = 0 then isnull(count(Distinct CxfGuiasVol.Lacre),0) else 0 end entPdLacres,\n"
                    + "Case when Rt_Perc.ER = 'R' and len(isnull(Rt_Perc.HrCheg,'')) > 0 then count(Distinct Rt_Perc.Parada) else 0 end recOk,\n"
                    + "Case when Rt_Perc.ER = 'R' and len(isnull(Rt_Perc.HrCheg,'')) = 0 then count(Distinct Rt_Perc.Parada) else 0 end recPd,\n"
                    + "Case when Rt_Perc.ER = 'R' and len(isnull(Rt_Perc.HrCheg,'')) > 0 then count(Distinct Rt_Guias.Guia) else 0 end recGuias,\n"
                    + "Case when Rt_Perc.ER = 'R' and len(isnull(Rt_Perc.HrCheg,'')) > 0 then isnull(count(Distinct CxfGuiasVol.Lacre),0) else 0 end recLacres,\n"
                    + "Case when Rt_Perc.ER = 'R' and len(isnull(Rt_Perc.HrCheg,'')) > 0 then ((isnull(sum(Distinct Rt_Guias.Valor),0)) / (select top 01 isnull(Round(Valor,5),1) \n"
                    + "	 from tesmoedasvlr \n"
                    + "	 where CodFil = Rotas.CodFil \n"
                    + "    and Convert(Date, DtCotacao) <= Convert(Date,Getdate()-1) \n"
                    + "	   and CodMoeda = (Case when Rt_GuiasMoeda.Moeda is not null then Rt_GuiasMoeda.Moeda \n"
                    + "				when Pedido.TipoMoeda is not null then Pedido.TipoMoeda\n"
                    + "				else MoedaPdrMobile end) Order by DtCotacao Desc)) else 0 end recValor,\n"
                    + "  (\n"
                    + "  Select sum(za.Valor) from (\n"
                    + "  Select ((Isnull(CxfGuias.Valor,0)) / \n"
                    + "                    	(select top 01 isnull(Round(Valor,5),1) \n"
                    + "                    	 from tesmoedasvlr \n"
                    + "                    	 where tesmoedasvlr.CodFil = Rotas.CodFil\n"
                    + "                            and Convert(Date, DtCotacao) <= Convert(Date,Getdate()-1) \n"
                    + "                    	   and CodMoeda = (Case when xy.Moeda is not null then xy.Moeda \n"
                    + "												when Pedido.TipoMoeda is not null then Pedido.TipoMoeda\n"
                    + "											else MoedaPdrMobile end) Order by DtCotacao Desc)) Valor,  CxfGuias.Guia, CxfGuias.Serie\n"
                    + "   From Rotas y\n"
                    + "   Left Join Rt_guias (NoLock)  on  Rt_Guias.Sequencia   = y.Sequencia\n"
                    + "                      and y.CodFil = Rotas.CodFil\n"
                    + "   Left join Rt_Perc x (NoLock) on x.Sequencia = Rt_Guias.Sequencia\n"
                    + "                    	                               and x.Parada = Rt_Guias.Parada \n"
                    + "   Left join Pedido (NoLock)  on Pedido.Numero = x.Pedido\n"
                    + "                    	                          and Pedido.CodFil = Rotas.CodFil\n"
                    + "   Left join Paramet (NoLock)  on Paramet.Filial_PDR = Rotas.CodFil\n"
                    + "   Left Join CxfGuias (NoLock)  on  CxfGuias.Guia    = Rt_Guias.Guia  \n"
                    + "                      and CxfGuias.Serie    = Rt_Guias.SerieAnt\n"
                    + "                      and CxfGuias.RotaEnt  = y.Rota \n"
                    + "                      and CXFGuias.Hora1    = x.Hora1 \n"
                    + "                      and CxfGuias.DtEnt = y.Data\n"
                    + "  Left join Rt_GuiasMoeda xy (Nolock) on xy.Sequencia = Rt_Guias.Sequencia\n"
                    + "                                   and xy.Parada = Rt_Guias.Parada\n"
                    + "								   and xy.Guia = Rt_Guias.Guia\n"
                    + "								   and xy.Serie = Rt_Guias.Serie\n"
                    + "  where y.Sequencia = Rotas.Sequencia\n"
                    + "    and CxfGuias.DtEnt is not Null ) za\n"
                    + "  ) ValorRecepCXF, \n"
                    + "   (Select sum(zy.Valor) from (\n"
                    + "                    	Select ((isnull(b.Valor,0)) / \n"
                    + "                    	(select top 01 isnull(Round(Valor,5),1) \n"
                    + "                    	 from tesmoedasvlr \n"
                    + "                    	 where tesmoedasvlr.CodFil = Rotas.CodFil \n"
                    + "                            and Convert(Date, DtCotacao) <= Convert(Date,Getdate()-1) \n"
                    + "                    	   and CodMoeda = (Case when xy.Moeda is not null then xy.Moeda \n"
                    + "												when Pedido.TipoMoeda is not null then Pedido.TipoMoeda\n"
                    + "											else MoedaPdrMobile end) Order by DtCotacao Desc)) Valor\n"
                    + "                    	 From Rt_Guias b (noLock) \n"
                    + "    Left join Rt_Perc c (noLock) on b.Sequencia = c.Sequencia \n"
                    + "                        and b.Parada = c.Parada\n"
                    + "    Left join Rotas (Nolock)  on Rotas.Sequencia = Rt_Perc.Sequencia\n"
                    + "   Left join Pedido (NoLock)  on Pedido.Numero = c.Pedido\n"
                    + "                    	                          and Pedido.CodFil = Rotas.Codfil\n"
                    + "   Left join Paramet (NoLock)  on Paramet.Filial_PDR = Rotas.CodFil \n"
                    + "     Left join Rt_GuiasMoeda xy (Nolock) on xy.Sequencia = b.Sequencia\n"
                    + "                                   and xy.Parada = b.Parada\n"
                    + "								   and xy.Guia = b.Guia\n"
                    + "								   and xy.Serie = b.Serie\n"
                    + "    Where b.Sequencia = Rt_Perc.Sequencia \n"
                    + "      and c.Hora1 = Rt_Perc.Hora1D \n"
                    + "      and Len(c.HrCheg) >= 5) zy\n"
                    + "  ) ValorEntDir,  \n"
                    + " (Select count(*) From Rt_Perc xa (nolock) \n"
                    + "   Where xa.Sequencia = Rt_Perc.Sequencia \n"
                    + "     and xa.Flag_Excl <> '*' \n"
                    + "	 and (len(xa.HrCheg) < 4 or xa.HrCheg is null) \n"
                    + "     and DateDiff(mi, Convert(Datetime, (Convert(varchar,Rotas.Data,112) + ' ' + Replace(Substring(xa.Hora1,1,2),':','0')+':'+Replace(Substring(xa.Hora1,3,2),':','0')+':00' )), getdate()) > 15 \n"
                    + "	 and Replace(Substring(xa.Hora1,1,2),':','0') between 00 and 23 \n"
                    + "	 and Replace(Substring(xa.Hora1,3,2),':','0') between 00 and 59 \n"
                    + ")  ServAtrasados, \n"
                    + "(Select Count(*) Qtde \n"
                    + "  from Rt_Perc a (noLock)\n"
                    + "  Left Join Rotas b (noLock) on b.Sequencia = a.Sequencia\n"
                    + "  where a.Sequencia = Rotas.Sequencia\n"
                    + "    and a.Flag_Excl <> '*'\n"
                    //+ "    and a.CodCli1 not in (Select CodCli from CxForte where CxForte.CodFil = b.CodFil)\n"
                    + "    and a.Parada = Rt_Perc.Parada\n"
                    + "    and a.HrCheg is not null \n"
                    + "    and a.HrCheg <> ''\n"
                    + "    and abs(DateDiff(mi, Case when len(a.Hora1) = 4 then Substring(a.Hora1,1,2)+':'+Substring(a.Hora1,3,2) else a.Hora1 end, a.HrCheg)) between 0 and 15) SrvEfetivos, \n"
                    + "(Select Count(*) Qtde  \n"
                    + "  from Rt_Perc a (noLock)\n"
                    + "  Left Join Rotas b (noLock) on b.Sequencia = a.Sequencia\n"
                    + "  where a.Sequencia = Rotas.Sequencia \n"
                    + "	  and a.Flag_Excl <> '*'\n"
                    //+ "	  and a.CodCli1 not in (Select CodCli from CxForte where CxForte.CodFil = b.CodFil)\n"
                    + "    and a.Parada = Rt_Perc.Parada\n"
                    + "	  and a.HrCheg is not null \n"
                    + "	  and a.HrCheg <> ''\n"
                    + "	  and DateDiff(mi, Case when len(a.Hora1) = 4 then Substring(a.Hora1,1,2)+':'+Substring(a.Hora1,3,2) else a.Hora1 end, a.HrCheg) < 0) SrvAdiantados, \n"
                    + "(Select Count(*) Qtde \n"
                    + "  from Rt_Perc a (noLock)\n"
                    + "  Left Join Rotas b (noLock) on b.Sequencia = a.Sequencia\n"
                    + "  where a.Sequencia = Rotas.Sequencia\n"
                    + "	  and a.Flag_Excl <> '*'\n"
                    //+ "	  and a.CodCli1 not in (Select CodCli from CxForte where CxForte.CodFil = b.CodFil)\n"
                    + "    and a.Parada = Rt_Perc.Parada\n"
                    + "	  and a.HrCheg is not null \n"
                    + "	  and a.HrCheg <> ''\n"
                    + "	  and DateDiff(mi, Case when len(a.Hora1) = 4 then Substring(a.Hora1,1,2)+':'+Substring(a.Hora1,3,2) else a.Hora1 end, a.HrCheg) > 15) SrvAtrasados, \n"
                    + "(Select Count(*) Qtde  \n"
                    + "from Rt_Perc a (noLock)\n"
                    + "Left Join Rotas b (noLock) on b.Sequencia = a.Sequencia\n"
                    + "where a.Sequencia = Rotas.Sequencia\n"
                    + "	and a.Flag_Excl <> '*'\n"
                    //+ "	and a.CodCli1 not in (Select CodCli from CxForte where CxForte.CodFil = b.CodFil)\n"
                    + "    and a.Parada = Rt_Perc.Parada\n"
                    + "	and (a.HrCheg is null or a.HrCheg = '')) SrvPendentes \n"
                    + "From Rt_Perc (nolock) \n"
                    + "   Left join Rt_PercDet  on Rt_PercDet.Sequencia = Rt_Perc.Sequencia\n"
                    + "                        and Rt_PercDet.Parada = Rt_Perc.Parada\n"
                    + "                        and Rt_PercDet.CodFil = Rt_Perc.CodFil\n"
                    + "                       AND Rt_PercDet.Km > 0\n"
                    + "Left join Rotas (nolock) on Rotas.Sequencia = Rt_Perc.Sequencia\n"
                    + "Left join Rt_Guias (nolock) on Rt_Guias.Sequencia = Rt_Perc.Sequencia\n"
                    + "                   and Rt_Guias.Parada = Rt_Perc.Parada\n"
                    + "Left join CxfGuiasVol (nolock) on CxfGuiasVol.Guia = Rt_Guias.Guia\n"
                    + "                      and CxfGuiasVol.Serie = Rt_Guias.Serie\n"
                    + "Left join Pedido (NoLock)  on Pedido.Numero = Rt_Perc.Pedido\n"
                    + "	   					and Pedido.CodFil = Rotas.Codfil\n"
                    + "Left join Paramet (NoLock)  on Paramet.Filial_PDR = Rotas.CodFil	\n"
                    + "  Left join Rt_GuiasMoeda  (Nolock) on Rt_GuiasMoeda.Sequencia = Rt_Guias.Sequencia\n"
                    + "                                   and Rt_GuiasMoeda.Parada = Rt_Guias.Parada\n"
                    + "					  and Rt_GuiasMoeda.Guia = Rt_Guias.Guia\n"
                    + "					  and Rt_GuiasMoeda.Serie = Rt_Guias.Serie\n"
                    + "Where Rotas.Data = " + data + " \n"
                    + " and Rt_Perc.Flag_Excl <> '*' \n"
                    + " and Rt_Perc.CodCli1 not in (Select CodCli from CxForte where CxForte.CodFil = Rotas.CodFil) \n"
                    + "Group by Rt_Perc.ER, Rt_Perc.HrCheg, Rt_Perc.Parada, Rt_Perc.Sequencia, Rt_Perc.Parada, Rt_Perc.Hora1, Rotas.Data, Rotas.CodFil, Rt_Perc.Hora1D, Pedido.TipoMoeda,Paramet.MoedaPdrMobile, Rotas.Sequencia, Rt_GuiasMoeda.Moeda, Rt_Perc.Pedido) z\n"
                    + "Group by z.Sequencia) y  on y.Sequencia = Escala.SeqRota\n"
                    + " Left join (Select Max(Codigo) Codigo, Matr from rastrear (nolock)\n"
                    + " where rastrear.Data =  " + data + " \n"
                    + " Group by Matr) ultimaComunicacao  on ultimaComunicacao.Codigo = Rastrear.Codigo\n"
                    + " where escala.Data =   " + data + " \n"
                    + (!codFil.equals("") ? "and escala.codFil = " + codFil + " \n" : "")
                    + "order by escala.rota");
        }
    }

    public List<Rastrear> posicoesRotas(String seqRota, Persistencia persistencia) throws Exception {
        try {
            List<Rastrear> retorno = new ArrayList<>();
            String sql = " SELECT Rastrear.* FROM Rastrear \n"
                    + " LEFT JOIN RastrearStat ON RastrearStat.Sequencia = Rastrear.Codigo\n"
                    + " WHERE RastrearStat.SeqRota = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(seqRota);
            consulta.select();
            Rastrear rastrear;
            while (consulta.Proximo()) {
                rastrear = new Rastrear();
                rastrear.setCodigo(consulta.getString("Codigo"));
                rastrear.setCODCLI(consulta.getString("CODCLI"));
                rastrear.setID_MODULO(consulta.getString("ID_MODULO"));
                rastrear.setPlaca(consulta.getString("Placa"));
                rastrear.setLatitude(consulta.getString("Latitude"));
                rastrear.setLongitude(consulta.getString("Longitude"));
                rastrear.setData(consulta.getLocalDate("Data"));
                rastrear.setHora(consulta.getString("Hora"));
                rastrear.setVelocidade(consulta.getString("Velocidade"));
                rastrear.setDirecao(consulta.getString("Direcao"));
                rastrear.setRua(consulta.getString("Rua"));
                rastrear.setCidade(consulta.getString("Cidade"));
                rastrear.setIntegrador(consulta.getString("Integrador"));
                rastrear.setEnviado(consulta.getString("Enviado"));
                rastrear.setEnvioMaspasR(consulta.getString("EnvioMaspasR"));
                rastrear.setEnvioCliente(consulta.getString("EnvioCliente"));
                rastrear.setInsereBD(consulta.getString("InsereBD"));
                rastrear.setDtTrans(consulta.getLocalDate("DtTrans"));
                rastrear.setHrTrans(consulta.getString("HrTrans"));
                rastrear.setCodMem(consulta.getString("CodMem"));
                rastrear.setSatelite(consulta.getString("Satelite"));
                rastrear.setGps(consulta.getString("Gps"));
                rastrear.setMovimento(consulta.getString("Movimento"));
                rastrear.setViolacao(consulta.getString("Violacao"));
                rastrear.setStatusPorta(consulta.getString("StatusPorta"));
                rastrear.setPrecisao(consulta.getString("Precisao"));
                rastrear.setMatr(consulta.getString("Matr"));
                rastrear.setBatida(consulta.getString("Batida"));
                retorno.add(rastrear);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("RastrearDao.posicoesRotas - " + e.getMessage() + "\r\n"
                    + " SELECT Rastrear.* FROM Rastrear \n"
                    + " LEFT JOIN RastrearStat ON RastrearStat.Sequencia = Rastrear.Codigo\n"
                    + " WHERE RastrearStat.SeqRota = " + seqRota);
        }
    }

    public List<Rastrear> posicaoSupervisores(String codFil, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            List<Rastrear> retorno = new ArrayList<>();
            sql = " SELECT\n"
                    + " RastrearEW.*,\n"
                    + " Pessoa.Nome\n"
                    + " FROM(\n"
                    + "      SELECT\n"
                    + "      MAX(Sequencia) Sequencia\n"
                    + "      FROM RastrearEW\n"
                    + "      GROUP BY CodPessoa\n"
                    + "     ) UltimaPosicao\n"
                    + " Left JOIN RastrearEW\n"
                    + "   ON UltimaPosicao.Sequencia = RastrearEW.Sequencia\n"
                    + " Left JOIN Pessoa\n"
                    + "   ON RastrearEW.CodPessoa = Pessoa.Codigo\n"
                    + " Left JOIN saspw\n"
                    + "   ON RastrearEW.CodPessoa = saspw.CodPessoa\n"
                    + " Left JOIN saspwac\n"
                    + "   ON saspw.Nome = saspwac.Nome\n"
                    + " Left JOIN Funcion\n"
                    + "  ON Pessoa.Matr = Funcion.Matr\n"
                    + " AND saspw.CodFil = Funcion.CodFil\n"
                    + " WHERE saspwac.Sistema = 302001\n"
                    + " AND   saspw.CodFil = ?"
                    + " AND   Funcion.Situacao <> 'D'"
                    + " ORDER BY Pessoa.Nome";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.select();
            Rastrear rastrear;

            while (consulta.Proximo()) {
                rastrear = new Rastrear();
                rastrear.setLatitude(consulta.getString("Latitude"));
                rastrear.setLongitude(consulta.getString("Longitude"));
                rastrear.setData(consulta.getLocalDate("Data"));
                rastrear.setHora(consulta.getString("Hora"));
                rastrear.setNome(consulta.getString("Nome"));
                rastrear.setCodigo(consulta.getString("CodPessoa"));
                retorno.add(rastrear);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("RastrearDao.posicaoSupervisores - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }

    public List<Rastrear> posicaoSupervisoresTempoReal(String codPessoa, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            List<Rastrear> retorno = new ArrayList<>();
            sql = "select\n"
                    + "RastrearEWHist.*,\n"
                    + "Pessoa.Nome\n"
                    + "FROM(\n"
                    + "     SELECT\n"
                    + "     MAX(Sequencia) Sequencia\n"
                    + "     FROM RastrearEW\n"
                    + "     GROUP BY CodPessoa\n"
                    + "    ) UltimaPosicao\n"
                    + " Left JOIN RastrearEW\n"
                    + "  ON UltimaPosicao.Sequencia = RastrearEW.Sequencia\n"
                    + " Left JOIN Pessoa\n"
                    + "  ON RastrearEW.CodPessoa = Pessoa.Codigo\n"
                    + " Left JOIN saspw\n"
                    + "  ON RastrearEW.CodPessoa = saspw.CodPessoa\n"
                    + " Left JOIN Funcion\n"
                    + " ON Pessoa.Matr = Funcion.Matr\n"
                    + "AND saspw.CodFil = Funcion.CodFil\n"
                    + " Left JOIN RastrearEW RastrearEWHist\n"
                    + "  ON RastrearEWHist.CodPessoa = RastrearEW.CodPessoa\n"
                    + " AND  RastrearEWHist.Data = RastrearEW.Data\n"
                    + "WHERE Pessoa.Codigo = ?\n"
                    + "ORDER BY Pessoa.Nome";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codPessoa);
            consulta.select();
            Rastrear rastrear;

            while (consulta.Proximo()) {
                rastrear = new Rastrear();
                rastrear.setLatitude(consulta.getString("Latitude"));
                rastrear.setLongitude(consulta.getString("Longitude"));
                rastrear.setData(consulta.getLocalDate("Data"));
                rastrear.setHora(consulta.getString("Hora"));
                rastrear.setNome(consulta.getString("Nome"));
                rastrear.setCodigo(consulta.getString("CodPessoa"));
                retorno.add(rastrear);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("RastrearDao.posicaoSupervisores - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }

    public Rastrear posicaoCentral(String codFil, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select top 1 Clientes.Ende + ', ' + Clientes.Cidade + '/' + Clientes.Estado Cidade, \n"
                    + "Clientes.CodFil, Clientes.Latitude, Clientes.Longitude, Filiais.Descricao  \n"
                    + "From CXForte \n"
                    + "Left join Clientes  on Clientes.Codigo = CXForte.CodCli \n"
                    + "                   and Clientes.CodFil = CXForte.CodFil \n"
                    + "Left join Filiais   on Filiais.CodFil  = CXForte.CodFil \n"
                    + "Where CXForte.CodFil = ? \n"
                    + "Order by CXForte.DtFecha Desc";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.select();
            Rastrear retorno = new Rastrear();
            if (consulta.Proximo()) {
                retorno.setLatitude(consulta.getString("Latitude"));
                retorno.setLongitude(consulta.getString("Longitude"));
                retorno.setNome(consulta.getString("Descricao"));
                retorno.setCidade(consulta.getString("Cidade"));
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar localização central - " + e.getMessage());
        }
    }

    public Rastrear posicaoCentral(BigDecimal sequencia, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select top 1 Clientes.Ende + ', ' + Clientes.Cidade + '/' + Clientes.Estado Cidade, \n"
                    + "Clientes.CodFil, Clientes.Latitude, Clientes.Longitude, Filiais.Descricao  \n"
                    + "From CXForte \n"
                    + " Left join Rt_Perc        on CXForte.CodFil  = Rt_Perc.CodFil \n"
                    + "Left join Clientes  on Clientes.Codigo = CXForte.CodCli \n"
                    + "                   and Clientes.CodFil = CXForte.CodFil \n"
                    + "Left join Filiais   on Filiais.CodFil  = CXForte.CodFil \n"
                    + "Where Rt_Perc.Sequencia = ? \n"
                    + "Order by CXForte.DtFecha Desc";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(sequencia);
            consulta.select();
            Rastrear retorno = new Rastrear();
            if (consulta.Proximo()) {
                retorno.setLatitude(consulta.getString("Latitude"));
                retorno.setLongitude(consulta.getString("Longitude"));
                retorno.setNome(consulta.getString("Descricao"));
                retorno.setCidade(consulta.getString("Cidade"));
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar localização central - " + e.getMessage());
        }
    }

    public void inserirPosicaoIncilRegra30Min(String data, Persistencia persistencia) {
        String sql = "";

        try {

            sql = "DECLARE @dataReferencia VARCHAR(8);\n"
                    + " DECLARE @horaReferencia VARCHAR(5);\n"
                    + " DECLARE @horaReferencia30minAdd VARCHAR(5);\n"
                    + " DECLARE @horaReferencia30minSub VARCHAR(5);\n"
                    + " \n"
                    + " SET @dataReferencia = ?;\n"
                    + " SET @horaReferencia = CONVERT(VARCHAR(5),GETDATE(),114);\n"
                    + " SET @horaReferencia30minAdd = CONVERT(VARCHAR(5),DATEADD(\"mi\",  30, GETDATE()),114);\n"
                    + " SET @horaReferencia30minSub = CONVERT(VARCHAR(5),DATEADD(\"mi\", -30, GETDATE()),114);\n"
                    + " \n"
                    + " \n"
                    + " /* Montar Tabela temporária com Rotas/Dados para Insert */\n"
                    + " SELECT\n"
                    + " ROW_NUMBER() OVER(ORDER BY Rotas.Sequencia ASC) AS Contador,\n"
                    + " Rotas.CodFil,\n"
                    + " Rotas.Sequencia SeqRota,\n"
                    + " Escala.MatrChe,\n"
                    + " Pessoa.Codigo CodPessoa,\n"
                    + " @dataReferencia DataPos,\n"
                    + " @horaReferencia HoraPos,\n"
                    + " @dataReferencia Data,\n"
                    + " @horaReferencia Hora\n"
                    + " INTO #tmpRotasPosicao\n"
                    + " FROM Rotas\n"
                    + " LEFT JOIN (SELECT\n"
                    + "            SeqRota,\n"
                    + "            COUNT(*) qtde\n"
                    + "            FROM RastrearStat\n"
                    + "            WHERE Data = @dataReferencia\n"
                    + "            GROUP BY SeqRota) RastrearStatQtde\n"
                    + "   ON Rotas.Sequencia = RastrearStatQtde.SeqRota\n"
                    + " LEFT JOIN Escala\n"
                    + "   ON Rotas.Rota   = Escala.Rota\n"
                    + "  AND Rotas.CodFil = Escala.CodFil\n"
                    + "  AND Rotas.Data   = Escala.Data\n"
                    + "  Left JOIN Pessoa\n"
                    + "   ON Escala.MatrChe = Pessoa.Matr  \n"
                    + " WHERE Rotas.Data = @dataReferencia\n"
                    + " AND   (RastrearStatQtde.qtde IS NULL OR RastrearStatQtde.qtde <= 1)\n"
                    + " AND   (CAST(@horaReferencia30minAdd as time) > CAST(Rotas.HrLargada as time) OR CAST(@horaReferencia30minSub as time) < CAST(Rotas.HrLargada as time))\n"
                    + " ORDER BY Rotas.Sequencia;\n"
                    + " \n"
                    + " \n"
                    + " /* Excluir Posição em Tabela Rastrear e RastrearStat */\n"
                    + " DELETE Rastrear\n"
                    + " FROM RastrearStat\n"
                    + "  Left JOIN Rastrear     \n"
                    + "   ON RastrearStat.Sequencia = Rastrear.Codigo\n"
                    + " WHERE RastrearStat.SeqRota IN (SELECT SeqRota FROM #tmpRotasPosicao) ;\n"
                    + " \n"
                    + " DELETE FROM RastrearStat WHERE SeqRota IN (SELECT SeqRota FROM #tmpRotasPosicao); \n"
                    + " \n"
                    + " \n"
                    + " /* Inserir Posição em RastrearStat */\n"
                    + " INSERT INTO RastrearStat (Sequencia,\n"
                    + "                           CodFil,\n"
                    + "                           SeqRota,\n"
                    + "                           CodPessoa,\n"
                    + "                           DataPOS,\n"
                    + "                           HoraPOS,\n"
                    + "                           Data,\n"
                    + "                           Hora)\n"
                    + " SELECT\n"
                    + " ISNULL((SELECT (MAX(Sequencia) + tblReferencia.Contador) FROM RastrearStat),1),\n"
                    + " tblReferencia.CodFil,\n"
                    + " tblReferencia.SeqRota,\n"
                    + " tblReferencia.CodPessoa,\n"
                    + " tblReferencia.DataPos,\n"
                    + " tblReferencia.HoraPos,\n"
                    + " tblReferencia.Data,\n"
                    + " tblReferencia.Hora\n"
                    + " FROM #tmpRotasPosicao tblReferencia;\n"
                    + " \n"
                    + " \n"
                    + " /* Inserir Posição em Rastrear */\n"
                    + " INSERT INTO Rastrear (Codigo, \n"
                    + "                       Latitude, \n"
                    + "                       Longitude, \n"
                    + "                       Data, \n"
                    + "                       Hora, \n"
                    + "                       DtTrans, \n"
                    + "                       HrTrans, \n"
                    + "                       Satelite, \n"
                    + "                       Matr) \n"
                    + " SELECT\n"
                    + " ISNULL((SELECT MAX(Sequencia) FROM RastrearStat WHERE SeqRota = tblReferencia.SeqRota),1),\n"
                    + " (SELECT TOP 1 Clientes.Latitude  FROM CXForte  Left JOIN Clientes ON CXForte.CodFil = Clientes.CodFil AND CXForte.CodCli = Clientes.Codigo WHERE CXForte.CodFil = tblReferencia.CodFil),\n"
                    + " (SELECT TOP 1 Clientes.Longitude FROM CXForte  Left JOIN Clientes ON CXForte.CodFil = Clientes.CodFil AND CXForte.CodCli = Clientes.Codigo WHERE CXForte.CodFil = tblReferencia.CodFil),\n"
                    + " tblReferencia.DataPos,\n"
                    + " tblReferencia.HoraPos,\n"
                    + " tblReferencia.Data,\n"
                    + " tblReferencia.Hora,\n"
                    + " 'Ger-SATMOB',\n"
                    + " tblReferencia.CodPessoa\n"
                    + " FROM #tmpRotasPosicao tblReferencia\n"
                    + " GROUP BY tblReferencia.codfil, tblReferencia.SeqRota,tblReferencia.DataPos,tblReferencia.HoraPos,tblReferencia.Data,tblReferencia.Hora,tblReferencia.CodPessoa;\n"
                    + " \n"
                    + " \n"
                    + " DROP TABLE #tmpRotasPosicao;";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(data);

            consulta.insert();
            consulta.close();

        } catch (Exception e) {
            // OBS: >> Pode não criar.... Se não houver Caixa Forte/Cliente Lat/Lon para Filial
            String msgErro = e.getMessage();
        }
    }

    public void inserirPosicaoIncilRegra30MinDiaSeguinte(String data, Persistencia persistencia) {
        String sql = "";

        try {

            sql = "DECLARE @dataReferencia VARCHAR(8);\n"
                    + " DECLARE @horaReferencia VARCHAR(5);\n"
                    + " \n"
                    + " SET @dataReferencia = ?;\n"
                    + " SET @horaReferencia = CONVERT(VARCHAR(5),GETDATE(),114);\n"
                    + " \n"
                    + " \n"
                    + " /* Montar Tabela temporária com Rotas/Dados para Insert */\n"
                    + " SELECT\n"
                    + " ROW_NUMBER() OVER(ORDER BY Rotas.Sequencia ASC) AS Contador,\n"
                    + " Rotas.CodFil,\n"
                    + " Rotas.Sequencia SeqRota,\n"
                    + " Escala.MatrChe,\n"
                    + " Pessoa.Codigo CodPessoa,\n"
                    + " Rotas.Data DataPos,\n"
                    + " @horaReferencia HoraPos,\n"
                    + " Rotas.Data Data,\n"
                    + " @horaReferencia Hora\n"
                    + " INTO #tmpRotasPosicao\n"
                    + " FROM Rotas\n"
                    + " LEFT JOIN (SELECT\n"
                    + "            SeqRota,\n"
                    + "            COUNT(*) qtde\n"
                    + "            FROM RastrearStat\n"
                    + "            WHERE Data > @dataReferencia\n"
                    + "            GROUP BY SeqRota) RastrearStatQtde\n"
                    + "   ON Rotas.Sequencia = RastrearStatQtde.SeqRota\n"
                    + " LEFT JOIN Escala\n"
                    + "   ON Rotas.Rota   = Escala.Rota\n"
                    + "  AND Rotas.CodFil = Escala.CodFil\n"
                    + "  AND Rotas.Data   = Escala.Data\n"
                    + " LEFT JOIN Pessoa\n"
                    + "   ON Escala.MatrChe = Pessoa.Matr  \n"
                    + " WHERE Rotas.Data > @dataReferencia\n"
                    + " AND   (RastrearStatQtde.qtde IS NULL OR RastrearStatQtde.qtde <= 1)\n"
                    + " ORDER BY Rotas.Sequencia;\n"
                    + " \n"
                    + " \n"
                    + " /* Excluir Posição em Tabela Rastrear e RastrearStat */\n"
                    + " DELETE Rastrear\n"
                    + " FROM RastrearStat\n"
                    + "  Left JOIN Rastrear     \n"
                    + "   ON RastrearStat.Sequencia = Rastrear.Codigo\n"
                    + " WHERE RastrearStat.SeqRota IN (SELECT SeqRota FROM #tmpRotasPosicao) ;\n"
                    + " \n"
                    + " DELETE FROM RastrearStat WHERE SeqRota IN (SELECT SeqRota FROM #tmpRotasPosicao); \n"
                    + " \n"
                    + " \n"
                    + " /* Inserir Posição em RastrearStat */\n"
                    + " INSERT INTO RastrearStat (Sequencia,\n"
                    + "                           CodFil,\n"
                    + "                           SeqRota,\n"
                    + "                           CodPessoa,\n"
                    + "                           DataPOS,\n"
                    + "                           HoraPOS,\n"
                    + "                           Data,\n"
                    + "                           Hora)\n"
                    + " SELECT\n"
                    + " ISNULL((SELECT (MAX(Sequencia) + tblReferencia.Contador) FROM RastrearStat),1),\n"
                    + " tblReferencia.CodFil,\n"
                    + " tblReferencia.SeqRota,\n"
                    + " tblReferencia.CodPessoa,\n"
                    + " tblReferencia.DataPos,\n"
                    + " tblReferencia.HoraPos,\n"
                    + " tblReferencia.Data,\n"
                    + " tblReferencia.Hora\n"
                    + " FROM #tmpRotasPosicao tblReferencia;\n"
                    + " \n"
                    + " \n"
                    + " /* Inserir Posição em Rastrear */\n"
                    + " INSERT INTO Rastrear (Codigo, \n"
                    + "                       Latitude, \n"
                    + "                       Longitude, \n"
                    + "                       Data, \n"
                    + "                       Hora, \n"
                    + "                       DtTrans, \n"
                    + "                       HrTrans, \n"
                    + "                       Satelite, \n"
                    + "                       Matr) \n"
                    + " SELECT\n"
                    + " ISNULL((SELECT MAX(Sequencia) FROM RastrearStat WHERE SeqRota = tblReferencia.SeqRota),1),\n"
                    + " (SELECT TOP 1 Clientes.Latitude  FROM CXForte  Left JOIN Clientes ON CXForte.CodFil = Clientes.CodFil AND CXForte.CodCli = Clientes.Codigo WHERE CXForte.CodFil = tblReferencia.CodFil ORDER BY CXForte.DtFecha DESC),\n"
                    + " (SELECT TOP 1 Clientes.Longitude FROM CXForte  Left JOIN Clientes ON CXForte.CodFil = Clientes.CodFil AND CXForte.CodCli = Clientes.Codigo WHERE CXForte.CodFil = tblReferencia.CodFil ORDER BY CXForte.DtFecha DESC),\n"
                    + " tblReferencia.DataPos,\n"
                    + " tblReferencia.HoraPos,\n"
                    + " tblReferencia.Data,\n"
                    + " tblReferencia.Hora,\n"
                    + " 'Ger-SATMOB',\n"
                    + " tblReferencia.CodPessoa\n"
                    + " FROM #tmpRotasPosicao tblReferencia;\n"
                    + " \n"
                    + " \n"
                    + " DROP TABLE #tmpRotasPosicao;";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(data);

            consulta.insert();
            consulta.close();

        } catch (Exception e) {
            // OBS: >> Pode não criar.... Se não houver Caixa Forte/Cliente Lat/Lon para Filial
            String msgErro = e.getMessage();
        }
    }

    public void inserirPosicaoInicial(Rt_Escala escalas, Double centroLat, Double centroLon, Persistencia persistencia) {
        try {
            StringBuilder str = new StringBuilder();

            str.append("DECLARE @CodFil AS INT;\n");
            str.append(" SET @CodFil = (SELECT TOP 1 CodFil FROM Rotas WHERE Sequencia = ?);");

            str.append(" DELETE Rastrear\n");
            str.append(" FROM RastrearStat\n");
            str.append("  Left JOIN Rastrear     \n");
            str.append("   ON RastrearStat.Sequencia = Rastrear.Codigo\n");
            str.append(" WHERE RastrearStat.SeqRota = ?; \n");
            str.append(" DELETE FROM RastrearStat WHERE SeqRota = ?; \n");

            str.append(" INSERT INTO RastrearStat (Sequencia, \n");
            str.append("                           CodFil, \n");
            str.append("                           SeqRota, \n");
            str.append("                           CodPessoa, \n");
            str.append("                           DataPOS, \n");
            str.append("                           HoraPOS, \n");
            str.append("                           Data, \n");
            str.append("                           Hora) VALUES(\n");
            str.append(" ISNULL((SELECT (MAX(Sequencia) + 1) FROM RastrearStat),1),\n");
            str.append(" @CodFil,\n");
            str.append(" ?,\n");
            str.append(" (SELECT Codigo FROM Pessoa WHERE Matr = ?),\n");
            str.append(" ?,\n");
            str.append(" ?,\n");
            str.append(" ?,\n");
            str.append(" ?);\n");

            str.append(" INSERT INTO Rastrear (Codigo, \n");
            str.append("                       Latitude, \n");
            str.append("                       Longitude, \n");
            str.append("                       Data, \n");
            str.append("                       Hora, \n");
            str.append("                       DtTrans, \n");
            str.append("                       HrTrans, \n");
            str.append("                       Satelite, \n");
            str.append("                       Matr) VALUES(\n");
            str.append(" ISNULL((SELECT MAX(Sequencia) FROM RastrearStat WHERE SeqRota = ?),1),\n");
            str.append(" ?,\n");
            str.append(" ?,\n");
            str.append(" ?,\n");
            str.append(" ?,\n");
            str.append(" ?,\n");
            str.append(" ?,\n");
            str.append(" 'Ger-SATMOB',\n");
            str.append(" (SELECT Codigo FROM Pessoa WHERE Matr = ?));\n");

            Consulta consulta = new Consulta(str.toString(), persistencia);

            // Parâmetros DECLARE
            consulta.setBigDecimal(escalas.getSequencia());

            // Parâmetros DELETE
            consulta.setBigDecimal(escalas.getSequencia());
            consulta.setBigDecimal(escalas.getSequencia());

            // Parâmetros RASTREAR-STAT
            consulta.setBigDecimal(escalas.getSequencia());
            consulta.setBigDecimal(escalas.getMatr());
            consulta.setString(escalas.getDt_alter());
            consulta.setString(escalas.getHr_alter());
            consulta.setString(escalas.getDt_alter());
            consulta.setString(escalas.getHr_alter());

            // Parâmetros RASTREAR
            consulta.setBigDecimal(escalas.getSequencia());
            consulta.setString(Double.toString(centroLat));
            consulta.setString(Double.toString(centroLon));
            consulta.setString(escalas.getDt_alter());
            consulta.setString(escalas.getHr_alter());
            consulta.setString(escalas.getDt_alter());
            consulta.setString(escalas.getHr_alter());
            consulta.setBigDecimal(escalas.getMatr());

            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            // OBS: >> Pode não criar.... Se não houver Caixa Forte/Cliente Lat/Lon para Filial
            String msgErro = e.getMessage();
        }
    }

    /**
     * Consulta trajeto do dia por SeqRota e Data
     *
     * @param Sequencia
     * @param Data
     * @param persistencia
     * @return
     * @throws Exception
     */
    public Rastrear consultarDadosTrajetosDia(BigDecimal Sequencia, String Data, Persistencia persistencia) throws Exception {
        StringBuilder sql = new StringBuilder();
        try {

            sql.append("DECLARE @CodigoSequecia AS int");
            sql.append(" DECLARE @DataTrajeto    AS varchar(10)");

            sql.append(" SET @CodigoSequecia = ?");
            sql.append(" SET @DataTrajeto    = ?");

            sql.append(" SELECT TOP 1");
            /* DADOS DO ULTIMO TRAJETO CONCLUIDO */
            sql.append(" TrajetoAnterior.Hora1 AS anterior_perc_hora,");
            sql.append(" TrajetoAnterior.latitude AS anterior_perc_lat,  ");
            sql.append(" TrajetoAnterior.longitude  AS anterior_perc_lon,");
            /* DADOS DA ULTIMA COMUNICACAO */
            sql.append(" UltimaComunicacao.hora AS ultima_comunicacao_hora,");
            sql.append(" UltimaComunicacao.latitude AS ultima_comunicacao_lat,");
            sql.append(" UltimaComunicacao.longitude AS ultima_comunicacao_lon,");
            /* DADOS DO TRAJETO EM ANDAMENTO */
            sql.append(" ProximoTrajeto.Hora1 AS prox_perc_hora1,");
            sql.append(" ProximoTrajeto.latitude AS prox_perc_lat,  ");
            sql.append(" ProximoTrajeto.longitude  AS prox_perc_lon");
            sql.append(" FROM rt_perc (nolock) ");
            sql.append("  Left JOIN Rotas         AS Rotas ");
            sql.append("   ON Rotas.Sequencia = rt_perc.sequencia ");
            sql.append("  AND Rotas.Flag_Excl <> '*'");
            sql.append("  Left JOIN Clientes      AS CliOri ");
            sql.append("   ON CliOri.Codigo = rt_perc.CodCli1 ");
            sql.append("  AND CliOri.CodFil = Rotas.CodFil ");
            sql.append(" LEFT JOIN Clientes AS CliDst ");
            sql.append("   ON CliDst.Codigo = rt_perc.CodCli2");
            sql.append("  AND CliDst.CodFil = Rotas.CodFil ");
            sql.append(" LEFT JOIN (SELECT TOP 1");
            sql.append("            X.sequencia,");
            sql.append("            X.Hora1,");
            sql.append("            X.CodCli1,");
            sql.append("            Z.latitude,");
            sql.append("            Z.longitude");
            sql.append("            FROM rt_perc       AS X");
            sql.append("             Left JOIN Rotas         AS Y ");
            sql.append("              ON X.sequencia = Y.Sequencia");
            sql.append("             AND Y.Flag_Excl <> '*'");
            sql.append("             Left JOIN Clientes     AS Z ");
            sql.append("              ON X.CodCli1 = Z.Codigo");
            sql.append("             AND Y.CodFil = Z.CodFil");
            sql.append("            WHERE X.sequencia = @CodigoSequecia");
            sql.append("            AND   X.HrCheg  IS NULL");
            sql.append("            AND   X.HrSaida IS NULL");
            sql.append("            AND   LEN(Z.latitude) > 0 ");
            sql.append("            AND   LEN(Z.longitude) > 0");
            sql.append("            AND   X.flag_excl <> '*'  ");
            sql.append("            GROUP BY X.sequencia, X.CodCli1, Z.latitude, Z.longitude, X.Hora1");
            sql.append("            ORDER BY X.Hora1) AS ProximoTrajeto");
            sql.append("   ON rt_perc.sequencia = ProximoTrajeto.sequencia");
            sql.append(" LEFT JOIN (SELECT TOP 1");
            sql.append("            X.sequencia,");
            sql.append("            X.Hora1 AS Hora1,");
            sql.append("            X.CodCli1,");
            sql.append("            Z.latitude,");
            sql.append("            Z.longitude");
            sql.append("            FROM rt_perc       AS X");
            sql.append("            Left JOIN Rotas         AS Y ");
            sql.append("              ON X.sequencia = Y.Sequencia");
            sql.append("             AND Y.Flag_Excl <> '*'");
            sql.append("            Left JOIN Clientes     AS Z ");
            sql.append("              ON X.CodCli1 = Z.Codigo");
            sql.append("             AND Y.CodFil = Z.CodFil");
            sql.append("            WHERE X.sequencia = @CodigoSequecia");
            sql.append("            AND   X.HrCheg  IS NOT NULL");
            sql.append("            AND   X.HrSaida IS NOT NULL");
            sql.append("            AND   LEN(Z.latitude) > 0 ");
            sql.append("            AND   LEN(Z.longitude) > 0");
            sql.append("            AND   X.flag_excl <> '*'  ");
            sql.append("            GROUP BY X.sequencia, X.CodCli1, Z.latitude, Z.longitude,X.Hora1, X.HrSaida");
            sql.append("            ORDER BY X.HrSaida DESC) AS TrajetoAnterior");
            sql.append("   ON rt_perc.sequencia = TrajetoAnterior.sequencia");
            sql.append(" LEFT JOIN (SELECT ");
            sql.append("            escala.SeqRota,");
            sql.append("            escala.rota,  ");
            sql.append("            escala.codfil, ");
            sql.append("            rastrear.latitude, ");
            sql.append("            rastrear.longitude, ");
            sql.append("            rastrear.data, ");
            sql.append("            rastrear.hora");
            sql.append("            FROM rastrearstat (nolock) ");
            sql.append("            Left JOIN rastrear (nolock) ");
            sql.append("              ON rastrearstat.sequencia = rastrear.codigo");
            sql.append("            Left JOIN escala (nolock) ");
            sql.append("              ON rastrearstat.seqrota = escala.seqrota");
            sql.append("             AND rastrearstat.codfil = escala.codfil");
            sql.append("            WHERE rastrearstat.Sequencia = (SELECT MAX (Sequencia) ");
            sql.append("                                            FROM rastrearstat");
            sql.append("                                            WHERE rastrearstat.SeqRota = @CodigoSequecia");
            sql.append("                                            AND   rastrearstat.Data    = @DataTrajeto");
            sql.append("                                            GROUP BY SeqRota)  ");
            sql.append("            AND   escala.SeqRota =  @CodigoSequecia) AS UltimaComunicacao");
            sql.append("   ON rt_perc.sequencia = UltimaComunicacao.SeqRota");
            sql.append(" WHERE rt_perc.sequencia = @CodigoSequecia");
            sql.append(" AND   rt_perc.flag_excl <> '*'  ");
            sql.append(" AND   len(cliori.latitude) > 1 ");
            sql.append(" AND   len(cliori.longitude) > 1");
            sql.append(" ORDER BY CASE WHEN len(Rt_Perc.HrCheg) > 0 THEN REPLACE(Rt_Perc.HrCheg,':','') ELSE rt_Perc.Hora1 END");

            Consulta consulta = new Consulta(sql.toString(), persistencia);
            consulta.setBigDecimal(Sequencia);
            consulta.setString(Data);
            consulta.select();

            Rastrear retorno = new Rastrear();

            if (consulta.Proximo()) {
                retorno.setTrajetoDiaPontoAntHora(consulta.getString("anterior_perc_hora"));
                retorno.setTrajetoDiaPontoAntLat(consulta.getString("anterior_perc_lat"));
                retorno.setTrajetoDiaPontoAntLon(consulta.getString("anterior_perc_lon"));

                retorno.setTrajetoDiaPontoUltComHora(consulta.getString("ultima_comunicacao_hora"));
                retorno.setTrajetoDiaPontoUltComLat(consulta.getString("ultima_comunicacao_lat"));
                retorno.setTrajetoDiaPontoUltComLon(consulta.getString("ultima_comunicacao_lon"));

                retorno.setTrajetoDiaPontoProxHora(consulta.getString("prox_perc_hora1"));
                retorno.setTrajetoDiaPontoProxLat(consulta.getString("prox_perc_lat"));
                retorno.setTrajetoDiaPontoProxLon(consulta.getString("prox_perc_lon"));
            }

            consulta.close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao consultar trajetos do dia - " + e.getMessage());
        }
    }
}
