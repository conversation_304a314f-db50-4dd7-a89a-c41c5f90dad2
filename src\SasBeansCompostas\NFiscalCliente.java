package SasBeansCompostas;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * Bean para representar dados de nota fiscal do cliente no portal
 * 
 * <AUTHOR>
 */
public class NFiscalCliente {
    
    private String praca;
    private String numero;
    private String serie;
    private LocalDate data;
    private String nRed;
    private String nomeCliente;
    private String tipoServico;
    private BigDecimal valor;
    private BigDecimal valorLiq;
    private LocalDate dtVenc;
    
    public NFiscalCliente() {
        this.valor = BigDecimal.ZERO;
        this.valorLiq = BigDecimal.ZERO;
    }

    public String getPraca() {
        return praca;
    }

    public void setPraca(String praca) {
        this.praca = praca;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getSerie() {
        return serie;
    }

    public void setSerie(String serie) {
        this.serie = serie;
    }

    public LocalDate getData() {
        return data;
    }

    public void setData(LocalDate data) {
        this.data = data;
    }

    public String getNRed() {
        return nRed;
    }

    public void setNRed(String nRed) {
        this.nRed = nRed;
    }

    public String getNomeCliente() {
        return nomeCliente;
    }

    public void setNomeCliente(String nomeCliente) {
        this.nomeCliente = nomeCliente;
    }

    public String getTipoServico() {
        return tipoServico;
    }

    public void setTipoServico(String tipoServico) {
        this.tipoServico = tipoServico;
    }

    public BigDecimal getValor() {
        return valor;
    }

    public void setValor(BigDecimal valor) {
        this.valor = valor != null ? valor : BigDecimal.ZERO;
    }

    public BigDecimal getValorLiq() {
        return valorLiq;
    }

    public void setValorLiq(BigDecimal valorLiq) {
        this.valorLiq = valorLiq != null ? valorLiq : BigDecimal.ZERO;
    }

    public LocalDate getDtVenc() {
        return dtVenc;
    }

    public void setDtVenc(LocalDate dtVenc) {
        this.dtVenc = dtVenc;
    }
    
    /**
     * Retorna uma chave única para identificar a nota fiscal
     * @return String no formato "numero-praca"
     */
    public String getChave() {
        return numero + "-" + praca;
    }
}
